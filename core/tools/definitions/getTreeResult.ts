import { Tool } from "../..";

import { BUILT_IN_GROUP_NAME, BuiltInToolNames } from "../builtIn";
import { createSystemMessageExampleCall } from "../systemMessageTools/buildToolsSystemMessage";

export const getTreeResultTool: Tool = {
  type: "function",
  displayTitle: "Get Tree Structure",
  wouldLikeTo: "get the tree structure of {{{ targetDir }}}",
  isCurrently: "getting the tree structure of {{{ targetDir }}}",
  hasAlready: "got the tree structure of {{{ targetDir }}}",
  readonly: true,
  isInstant: true,
  group: BUILT_IN_GROUP_NAME,
  function: {
    name: BuiltInToolNames.GetTreeResult,
    description:
      "Get a tree-like directory structure of the project or a specific directory",
    parameters: {
      type: "object",
      properties: {
        targetDir: {
          type: "string",
          description:
            "The target directory path to get tree structure for. If not provided, will use the workspace root directory.",
        },
      },
    },
  },
  systemMessageDescription: createSystemMessageExampleCall(
    BuiltInToolNames.GetTreeResult,
    `To get a tree-like directory structure, call the ${BuiltInToolNames.GetTreeResult} tool with optional "targetDir". For example:`,
    [["targetDir", "src/components"]],
  ),
};

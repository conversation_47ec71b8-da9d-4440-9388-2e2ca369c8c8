import org.jetbrains.changelog.date
import org.jetbrains.changelog.markdownToHTML
import org.jetbrains.intellij.platform.gradle.TestFrameworkType

fun properties(key: String) = providers.gradleProperty(key)

fun environment(key: String) = providers.environmentVariable(key)

val isEap get() = environment("RELEASE_CHANNEL").orNull == "eap"

plugins {
    id("java") // Java support
    alias(libs.plugins.kotlin) // Kotlin support
    // alias(libs.plugins.gradleIntelliJPlugin) // Gradle IntelliJ Plugin
    alias(libs.plugins.intelliJPlatform) // IntelliJ Platform Gradle Plugin
    alias(libs.plugins.changelog) // Gradle Changelog Plugin
    alias(libs.plugins.qodana) // Gradle Qodana Plugin
    alias(libs.plugins.kover) // Gradle Kover Plugin
    alias(libs.plugins.kotlin.serialization) // Kotlin Serialization Plugin
    id("com.aimi.gradle.BinaryUploadPlugin") // AIMI Binary Upload Plugin
}

group = properties("pluginGroup").get()

//"${properties("pluginVersion").get()}-${date("yyyy-MM-dd HH:mm:ss")}"
val buildVersion = properties("pluginVersion").get()
version = buildVersion

// Configure project's dependencies
repositories {
    mavenCentral()
    maven { url = uri("https://packages.jetbrains.team/maven/p/ij/intellij-dependencies") }
    intellijPlatform {
        defaultRepositories()
    }
}

// Dependencies are managed with Gradle version catalog - read more:
// https://docs.gradle.org/current/userguide/platforms.html#sub:version-catalog
dependencies {
    // IntelliJ Platform Gradle Plugin Dependencies Extension - read more: https://plugins.jetbrains.com/docs/intellij/tools-intellij-platform-gradle-plugin-dependencies-extension.html
    intellijPlatform {
        create(providers.gradleProperty("platformType"), providers.gradleProperty("platformVersion"))
        // local("${System.getProperty("user.home")}/Applications/Android Studio.app")
        // local("${System.getProperty("user.home")}/Applications/IntelliJ IDEA Ultimate.app")

        // Plugin Dependencies. Uses `platformBundledPlugins` property from the gradle.properties file for bundled IntelliJ Platform plugins.
        bundledPlugins(providers.gradleProperty("platformBundledPlugins").map { it.split(',') })

        // Plugin Dependencies. Uses `platformPlugins` property from the gradle.properties file for plugin from JetBrains Marketplace.
        plugins(providers.gradleProperty("platformPlugins").map { it.split(',') })

        pluginVerifier()
        zipSigner()
        testFramework(TestFrameworkType.Platform)
    }
    //    implementation(libs.annotations)
    implementation(libs.okhttp) {
        val stdlib = libs.kotlin.stdlib.get()
        exclude(group = stdlib.group, module = stdlib.name)
    }
    implementation(libs.kotlin.stdlib)
    implementation(libs.posthog)
    implementation(libs.kotlinx.serialization.json)
    implementation(libs.video.recorder)
    implementation("com.google.code.gson:gson:2.8.9") // Add Gson for JSON parsing


    // Exclude vulnerable Log4j from all dependencies
    configurations.all {
        resolutionStrategy {
            eachDependency {
                if (requested.group == "log4j" && requested.name == "log4j") {
                    useTarget(libs.log4j.over.slf4j)
                }
            }
        }
    }

    // Add Log4j 2.x explicitly
    implementation(libs.log4j.core)
    implementation(libs.log4j.api)

    testImplementation(libs.remote.robot)
    testImplementation(libs.remote.fixtures)
    testImplementation(libs.jupiter.api)
    testImplementation(libs.junit)
    testRuntimeOnly(libs.jupiter.engine)
    testImplementation(libs.logging.interceptor)

    testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3")
    testImplementation("io.mockk:mockk:1.14.2")
    testImplementation(kotlin("test"))
    testImplementation("com.automation-remarks:video-recorder-junit5:2.0")
    testRuntimeOnly("org.junit.vintage:junit-vintage-engine:5.10.0") // required to run both JUnit 5 and JUnit 3
}

// Set the JVM language level used to build the project. Use Java 11 for 2020.3+, and Java 17 for
// 2022.2+.
kotlin { jvmToolchain(17) }
java.sourceSets.main {
    java.srcDirs("src/main/kotlin")
}

// Configure Gradle IntelliJ Plugin - read more:
// https://plugins.jetbrains.com/docs/intellij/tools-gradle-intellij-plugin.html
intellijPlatform {
    pluginConfiguration {
        ideaVersion {
            sinceBuild = properties("pluginSinceBuild").get()
            untilBuild = properties("pluginUntilBuild").get()
        }
        version.set(buildVersion)
        description = providers.fileContents(layout.projectDirectory.file("README.md")).asText.get()
            .substringAfter("<!-- Plugin description -->")
            .substringBefore("<!-- Plugin description end -->")
            .let(::markdownToHTML)
        check(description.get().isNotEmpty()) { "Plugin description section not found in README.md" }
        // 用这个属性存构建时间
        vendor.url = date("yyyy-MM-dd HH:mm")
        println("buildTime: ${vendor.url.get()}")
    }

    signing {
        certificateChain = environment("CERTIFICATE_CHAIN")
        privateKey = environment("PRIVATE_KEY")
        password = environment("PRIVATE_KEY_PASSWORD")
    }

    publishing {
        host = "http://ide.alibaba-inc.com"
        token = "aone"
        val channel = if (isEap) "eap" else "default"
        channels = listOf(channel)
    }

    pluginVerification {
        ides {
            recommended()
        }
    }

}

// Configure Gradle Changelog Plugin - read more:
// https://github.com/JetBrains/gradle-changelog-plugin
changelog {
    groups.empty()
    repositoryUrl = properties("pluginRepositoryUrl")
}

// Configure Gradle Qodana Plugin - read more: https://github.com/JetBrains/gradle-qodana-plugin
qodana {
    cachePath = provider { file(".qodana").canonicalPath }
    resultsPath = provider { file("build/reports/inspections").canonicalPath }
    // saveReport = true
    // showReport = environment("QODANA_SHOW_REPORT").map { it.toBoolean() }.getOrElse(false)
}

// Configure Gradle Kover Plugin - read more: https://github.com/Kotlin/kotlinx-kover#configuration
kover {
    reports {
        total {
            xml {
                onCheck = true
            }
        }
    }
}


tasks {
    prepareSandbox {
        val nameMap = mapOf("aimi-binary" to "aimi-binary-intellij")
        from("../../binary/bin") {
            rename { name -> nameMap[name] ?: name }
            into("${pluginName.get()}/core/")
        }
    }

    wrapper { gradleVersion = properties("gradleVersion").get() }

    runIde {
        // Open the `manual-testing-sandbox` directory and the specific file on start
        args = listOf(
            // "${rootProject.projectDir.parentFile.parentFile}",
            // "${rootProject.projectDir.parentFile.parentFile.parentFile}/ProxyAI",
            "${rootProject.projectDir.parentFile.parentFile.parentFile}/taolive_platform",
            // "/Users/<USER>/Desktop/mtl/taolive_platform/tao-live-crossplatform-foundation/src/commonMain/kotlin/com/taobao/live/liveroom/liveBizComponent/leftBottomGroup/sender/TaoLiveKtCommentSender.kt",
            // "${rootProject.projectDir.parentFile.parentFile.parentFile}/taolive_platform/build.gradle.kts",
            // "${rootProject.projectDir.parentFile.parentFile}/manual-testing-sandbox",
            // "${rootProject.projectDir.parentFile.parentFile}/manual-testing-sandbox/test.kt"
        ).map { file(it).absolutePath }

        // 启用k2
        jvmArgs("-Didea.kotlin.plugin.use.k2=true")
    }

    test {
        useJUnit()
        useJUnitPlatform()
    }
}

// Configure AIMI Binary Upload Plugin
binaryUpload {
    enabled = true
    ossEndpoint = "oss-cn-zhangjiakou.aliyuncs.com"
    bucketName = "aimi-binaries"
    region = "cn-zhangjiakou"
}

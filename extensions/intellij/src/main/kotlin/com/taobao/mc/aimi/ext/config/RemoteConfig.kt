package com.taobao.mc.aimi.ext.config

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json

/**
 * 远程配置数据类
 */
@Serializable
data class RemoteConfig(
    /**
     * 配置版本
     */
    val version: String,
    
    /**
     * 生成时间
     */
    val generatedAt: String,
    
    /**
     * 二进制文件下载配置
     */
    val binaries: Map<String, BinaryDownloadInfo>
) {
    companion object {
        private val json = Json { 
            prettyPrint = true
            ignoreUnknownKeys = true
        }
        
        /**
         * 从JSON字符串解析远程配置
         */
        fun fromJson(jsonString: String): RemoteConfig? {
            return try {
                json.decodeFromString<RemoteConfig>(jsonString)
            } catch (e: Exception) {
                null
            }
        }
    }
    
    /**
     * 转换为JSON字符串
     */
    fun toJson(): String {
        return json.encodeToString(serializer(), this)
    }
    
    /**
     * 获取指定架构的下载信息
     */
    fun getBinaryInfo(architecture: String): BinaryDownloadInfo? {
        return binaries[architecture]
    }
    
    /**
     * 获取所有支持的架构
     */
    fun getSupportedArchitectures(): Set<String> {
        return binaries.keys
    }
}

/**
 * 二进制文件下载信息
 */
@Serializable
data class BinaryDownloadInfo(
    /**
     * 下载URL
     */
    val downloadUrl: String,
    
    /**
     * 文件大小（字节）
     */
    val fileSize: Long? = null,
    
    /**
     * MD5校验和
     */
    val md5: String? = null,
    
    /**
     * SHA256校验和
     */
    val sha256: String? = null,
    
    /**
     * 版本信息
     */
    val version: String,
    
    /**
     * 架构信息
     */
    val architecture: String,
    
    /**
     * 构建时间
     */
    val buildTime: String? = null
)

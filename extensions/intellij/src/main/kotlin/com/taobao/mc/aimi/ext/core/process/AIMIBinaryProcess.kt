package com.taobao.mc.aimi.ext.core.process

import com.intellij.ide.plugins.PluginManagerCore
import com.intellij.openapi.extensions.PluginId
import com.intellij.openapi.project.Project
import com.jetbrains.rd.util.AtomicReference
import com.taobao.mc.aimi.ext.constants.AIMIConstants
import com.taobao.mc.aimi.ext.download.BinaryDownloadManager
import com.taobao.mc.aimi.ext.utils.ErrorHandler
import com.taobao.mc.aimi.ext.utils.OS
import com.taobao.mc.aimi.ext.utils.getAIMIBinaryPath
import com.taobao.mc.aimi.ext.utils.getOS
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.proxy.ProxySettings
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import java.io.File
import java.io.InputStream
import java.io.OutputStream
import java.nio.file.Files
import java.nio.file.Paths
import java.nio.file.attribute.PosixFilePermission
import java.util.concurrent.TimeUnit

class AIMIBinaryProcess(
    private val project: Project,
    private val onUnexpectedExit: () -> Unit
) : AIMIProcess {

    private val logger = LoggerManager.getLogger(javaClass)
    private val process = startBinaryProcess()
    private val manuallyClose = AtomicReference(false)
    override val input: InputStream = process.inputStream
    override val output: OutputStream = process.outputStream


    override fun close() {
        manuallyClose.compareAndSet(expectedValue = false, newValue = true);
        process.destroy()
        // 如果进程仍然存活，强制终止
        if (!process.waitFor(5, TimeUnit.SECONDS)) {
            process.destroyForcibly()
        }
    }

    private fun startBinaryProcess(): Process {
        // 1. 检查并下载二进制文件
        runBlocking(Dispatchers.IO) {
            ensureBinaryFilesAvailable()
        }

        val path = getAIMIBinaryPath()
        runBlocking(Dispatchers.IO) {
            setPermissions()
        }

        val builder = ProcessBuilder(path)
        val proxySettings = ProxySettings.getSettings()
        if (proxySettings.enabled)
            builder.environment() += "HTTP_PROXY" to proxySettings.proxy
        return builder
            .directory(File(path).parentFile)
            .start()
            .apply {
                onExit().thenRun {
                    if (!manuallyClose.get()) {
                        // 非手动退出时，才回调异常退出事件
                        onUnexpectedExit()
                    }
                }.thenRun(::reportErrorTelemetry)
            }
    }

    /**
     * 确保二进制文件可用
     */
    private fun ensureBinaryFilesAvailable() {
        ErrorHandler.safeExecute(project, "ensure binary files available") {
            // 获取当前插件版本
            val pluginId = AIMIConstants.PLUGIN_ID
            val plugin = PluginManagerCore.getPlugin(PluginId.getId(pluginId))
            val pluginVersion = plugin?.version

            if (pluginVersion == null) {
                throw RuntimeException("Cannot determine plugin version")
            }

            ErrorHandler.logProgress("ensure binary files available", "plugin version: $pluginVersion")

            // 创建下载管理器并检查二进制文件
            val downloadManager = BinaryDownloadManager(project)
            val success = downloadManager.checkAndDownloadBinaries(pluginVersion)

            if (!success) {
                throw RuntimeException("Failed to ensure binary files are available")
            }

            ErrorHandler.logSuccess("ensure binary files available", "binary files are ready")
        }
    }

    private fun reportErrorTelemetry() {
        var err = process.errorStream?.bufferedReader()?.readText()?.trim()
        if (err != null) {
            // There are often "⚡️Done in Xms" messages, and we want everything after the last one
            val delimiter = "⚡ Done in"
            val doneIndex = err.lastIndexOf(delimiter)
            if (doneIndex != -1) {
                err = err.substring(doneIndex + delimiter.length)
            }
        } else {
            err = process.errorReader().readText().trim()
        }
        logger.warn("Core process exited with output: $err")
    }

    private companion object {

        private fun setPermissions() {
            val os = getOS()
            when (os) {
                OS.MAC -> setMacOsPermissions()
                OS.WINDOWS -> {}
                OS.LINUX -> elevatePermissions()
            }
        }

        private fun setMacOsPermissions() {
            ProcessBuilder("xattr", "-dr", "com.apple.quarantine", getAIMIBinaryPath()).start().waitFor()
            elevatePermissions()
        }

        // todo: consider setting permissions ahead-of-time during build/packaging, not at runtime
        private fun elevatePermissions() {
            val path = getAIMIBinaryPath()
            val permissions = setOf(
                PosixFilePermission.OWNER_READ,
                PosixFilePermission.OWNER_WRITE,
                PosixFilePermission.OWNER_EXECUTE
            )
            Files.setPosixFilePermissions(Paths.get(path), permissions)
        }
    }

}

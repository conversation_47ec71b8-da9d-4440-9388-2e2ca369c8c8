package com.taobao.mc.aimi.ext.version

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import java.text.SimpleDateFormat
import java.util.*

/**
 * 版本信息数据类
 */
@Serializable
data class VersionInfo(
    /**
     * 插件版本号
     */
    val pluginVersion: String,
    
    /**
     * 二进制文件版本号
     */
    val binaryVersion: String,
    
    /**
     * 构建时间
     */
    val buildTime: String,
    
    /**
     * 目标平台架构
     */
    val targetPlatform: String,
    
    /**
     * Git提交哈希
     */
    val gitCommitHash: String? = null,
    
    /**
     * 构建环境信息
     */
    val buildEnvironment: String? = null
) {
    companion object {
        private val json = Json { 
            prettyPrint = true
            ignoreUnknownKeys = true
        }
        
        /**
         * 从JSON字符串解析版本信息
         */
        fun fromJson(jsonString: String): VersionInfo? {
            return try {
                json.decodeFromString<VersionInfo>(jsonString)
            } catch (e: Exception) {
                null
            }
        }
        
        /**
         * 创建当前版本信息
         */
        fun createCurrent(
            pluginVersion: String,
            targetPlatform: String,
            gitCommitHash: String? = null
        ): VersionInfo {
            val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            return VersionInfo(
                pluginVersion = pluginVersion,
                binaryVersion = pluginVersion, // 二进制版本与插件版本保持一致
                buildTime = dateFormat.format(Date()),
                targetPlatform = targetPlatform,
                gitCommitHash = gitCommitHash,
                buildEnvironment = System.getProperty("os.name")
            )
        }
    }
    
    /**
     * 转换为JSON字符串
     */
    fun toJson(): String {
        return json.encodeToString(serializer(), this)
    }
    
    /**
     * 检查版本是否匹配
     */
    fun isVersionMatch(otherPluginVersion: String): Boolean {
        return pluginVersion == otherPluginVersion
    }
    
    /**
     * 检查平台是否匹配
     */
    fun isPlatformMatch(otherPlatform: String): Boolean {
        return targetPlatform == otherPlatform
    }
    
    /**
     * 获取版本信息摘要
     */
    fun getSummary(): String {
        return "Plugin: $pluginVersion, Binary: $binaryVersion, Platform: $targetPlatform, Built: $buildTime"
    }
}

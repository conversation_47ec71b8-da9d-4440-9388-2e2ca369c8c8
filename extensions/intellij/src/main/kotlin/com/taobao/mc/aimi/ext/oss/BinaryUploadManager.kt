package com.taobao.mc.aimi.ext.oss

import com.taobao.mc.aimi.ext.utils.getOsAndArchTarget
import com.taobao.mc.aimi.ext.version.VersionManager
import com.taobao.mc.aimi.logger.LoggerManager
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream

/**
 * 二进制文件上传管理器
 * 负责压缩、上传二进制文件到OSS
 */
class BinaryUploadManager(
    private val ossConfig: OSSConfig,
    private val versionManager: VersionManager = VersionManager()
) {
    
    private val logger = LoggerManager.getLogger(javaClass)
    private val ossUploadService = OSSUploadService(ossConfig)
    
    /**
     * 压缩并上传二进制文件
     * 
     * @param coreDirectory core目录
     * @param pluginVersion 插件版本
     * @param gitCommitHash Git提交哈希（可选）
     * @return 上传结果信息
     */
    fun compressAndUpload(
        coreDirectory: File, 
        pluginVersion: String, 
        gitCommitHash: String? = null
    ): UploadResult {
        try {
            if (!coreDirectory.exists() || !coreDirectory.isDirectory) {
                return UploadResult.failure("Core directory does not exist: ${coreDirectory.absolutePath}")
            }
            
            // 1. 创建版本信息文件
            if (!versionManager.createVersionFile(coreDirectory, pluginVersion, gitCommitHash)) {
                return UploadResult.failure("Failed to create version file")
            }
            
            // 2. 按架构分别处理
            val results = mutableMapOf<String, Boolean>()
            val downloadUrls = mutableMapOf<String, String>()
            
            // 获取所有架构目录
            val archDirectories = coreDirectory.listFiles { file ->
                file.isDirectory && file.name.matches(Regex("\\w+-\\w+"))
            } ?: emptyArray()
            
            if (archDirectories.isEmpty()) {
                return UploadResult.failure("No architecture directories found in core directory")
            }
            
            for (archDir in archDirectories) {
                val archName = archDir.name
                logger.info("Processing architecture: $archName")
                
                // 3. 压缩架构目录
                val zipFile = createArchiveForArch(archDir, pluginVersion)
                if (zipFile == null) {
                    results[archName] = false
                    continue
                }
                
                try {
                    // 4. 上传到OSS
                    val objectKey = generateObjectKey(archName, pluginVersion)
                    val metadata = createUploadMetadata(pluginVersion, archName, gitCommitHash)
                    
                    val uploadSuccess = ossUploadService.uploadFile(zipFile, objectKey, metadata)
                    results[archName] = uploadSuccess
                    
                    if (uploadSuccess) {
                        downloadUrls[archName] = ossUploadService.getDownloadUrl(objectKey)
                        logger.info("Successfully uploaded $archName: ${downloadUrls[archName]}")
                    } else {
                        logger.error("Failed to upload $archName")
                    }
                    
                } finally {
                    // 清理临时文件
                    zipFile.delete()
                }
            }
            
            // 5. 检查上传结果
            val successCount = results.values.count { it }
            val totalCount = results.size
            
            return if (successCount == totalCount) {
                UploadResult.success(downloadUrls, "All $totalCount architectures uploaded successfully")
            } else {
                UploadResult.partial(downloadUrls, "Only $successCount of $totalCount architectures uploaded successfully")
            }
            
        } catch (e: Exception) {
            logger.error("Failed to compress and upload binary files", e)
            return UploadResult.failure("Upload failed: ${e.message}")
        }
    }
    
    /**
     * 为指定架构创建压缩包
     */
    private fun createArchiveForArch(archDir: File, pluginVersion: String): File? {
        try {
            val archName = archDir.name
            val tempDir = File(System.getProperty("java.io.tmpdir"))
            val zipFile = File(tempDir, "aimi-binary-$archName-$pluginVersion.zip")
            
            ZipOutputStream(FileOutputStream(zipFile)).use { zipOut ->
                addDirectoryToZip(archDir, "", zipOut)
            }
            
            logger.info("Created archive for $archName: ${zipFile.absolutePath} (${zipFile.length()} bytes)")
            return zipFile
            
        } catch (e: Exception) {
            logger.error("Failed to create archive for ${archDir.name}", e)
            return null
        }
    }
    
    /**
     * 递归添加目录到ZIP
     */
    private fun addDirectoryToZip(sourceDir: File, parentPath: String, zipOut: ZipOutputStream) {
        val files = sourceDir.listFiles() ?: return
        
        for (file in files) {
            val entryPath = if (parentPath.isEmpty()) file.name else "$parentPath/${file.name}"
            
            if (file.isDirectory) {
                // 添加目录条目
                zipOut.putNextEntry(ZipEntry("$entryPath/"))
                zipOut.closeEntry()
                // 递归添加子目录
                addDirectoryToZip(file, entryPath, zipOut)
            } else {
                // 添加文件
                zipOut.putNextEntry(ZipEntry(entryPath))
                FileInputStream(file).use { fileIn ->
                    fileIn.copyTo(zipOut)
                }
                zipOut.closeEntry()
            }
        }
    }
    
    /**
     * 生成OSS对象键
     */
    private fun generateObjectKey(archName: String, pluginVersion: String): String {
        return "${ossConfig.binaryPathPrefix}/$pluginVersion/$archName/aimi-binary-$archName.zip"
    }
    
    /**
     * 创建上传元数据
     */
    private fun createUploadMetadata(
        pluginVersion: String, 
        archName: String, 
        gitCommitHash: String?
    ): Map<String, String> {
        val metadata = mutableMapOf(
            "version" to pluginVersion,
            "architecture" to archName,
            "build-time" to java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date()),
            "uploader" to "aimi-intellij-plugin"
        )
        
        gitCommitHash?.let { metadata["git-commit"] = it }
        
        return metadata
    }
}

/**
 * 上传结果
 */
data class UploadResult(
    val success: Boolean,
    val downloadUrls: Map<String, String>,
    val message: String,
    val isPartial: Boolean = false
) {
    companion object {
        fun success(downloadUrls: Map<String, String>, message: String): UploadResult {
            return UploadResult(true, downloadUrls, message)
        }
        
        fun failure(message: String): UploadResult {
            return UploadResult(false, emptyMap(), message)
        }
        
        fun partial(downloadUrls: Map<String, String>, message: String): UploadResult {
            return UploadResult(true, downloadUrls, message, true)
        }
    }
}

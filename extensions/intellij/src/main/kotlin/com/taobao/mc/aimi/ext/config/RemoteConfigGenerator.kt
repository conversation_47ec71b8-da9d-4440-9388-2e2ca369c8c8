package com.taobao.mc.aimi.ext.config

import com.taobao.mc.aimi.ext.oss.UploadResult
import com.taobao.mc.aimi.logger.LoggerManager
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

/**
 * 远程配置生成器
 * 负责生成remote.json配置文件
 */
class RemoteConfigGenerator {
    
    private val logger = LoggerManager.getLogger(javaClass)
    
    /**
     * 生成远程配置文件
     * 
     * @param uploadResult 上传结果
     * @param pluginVersion 插件版本
     * @param resourcesDir resources目录
     * @return 生成是否成功
     */
    fun generateRemoteConfig(
        uploadResult: UploadResult,
        pluginVersion: String,
        resourcesDir: File
    ): Boolean {
        try {
            if (!uploadResult.success || uploadResult.downloadUrls.isEmpty()) {
                logger.error("Cannot generate remote config: upload was not successful or no download URLs available")
                return false
            }
            
            // 确保resources目录存在
            if (!resourcesDir.exists()) {
                resourcesDir.mkdirs()
            }
            
            // 创建二进制下载信息映射
            val binaries = uploadResult.downloadUrls.mapValues { (architecture, downloadUrl) ->
                BinaryDownloadInfo(
                    downloadUrl = downloadUrl,
                    version = pluginVersion,
                    architecture = architecture,
                    buildTime = getCurrentTimestamp()
                )
            }
            
            // 创建远程配置
            val remoteConfig = RemoteConfig(
                version = pluginVersion,
                generatedAt = getCurrentTimestamp(),
                binaries = binaries
            )
            
            // 写入配置文件
            val configFile = File(resourcesDir, "remote.json")
            configFile.writeText(remoteConfig.toJson())
            
            logger.info("Remote config generated successfully: ${configFile.absolutePath}")
            logger.info("Config contains ${binaries.size} architectures: ${binaries.keys.joinToString(", ")}")
            
            return true
            
        } catch (e: Exception) {
            logger.error("Failed to generate remote config", e)
            return false
        }
    }
    
    /**
     * 读取现有的远程配置
     * 
     * @param resourcesDir resources目录
     * @return 远程配置，如果不存在或读取失败返回null
     */
    fun readRemoteConfig(resourcesDir: File): RemoteConfig? {
        try {
            val configFile = File(resourcesDir, "remote.json")
            if (!configFile.exists()) {
                logger.info("Remote config file does not exist: ${configFile.absolutePath}")
                return null
            }
            
            val jsonContent = configFile.readText()
            val remoteConfig = RemoteConfig.fromJson(jsonContent)
            
            if (remoteConfig != null) {
                logger.info("Remote config read successfully: version=${remoteConfig.version}, architectures=${remoteConfig.getSupportedArchitectures().joinToString(", ")}")
            } else {
                logger.error("Failed to parse remote config file: ${configFile.absolutePath}")
            }
            
            return remoteConfig
            
        } catch (e: Exception) {
            logger.error("Failed to read remote config from $resourcesDir", e)
            return null
        }
    }
    
    /**
     * 验证远程配置的有效性
     * 
     * @param remoteConfig 远程配置
     * @param expectedVersion 期望的版本
     * @return 配置是否有效
     */
    fun validateRemoteConfig(remoteConfig: RemoteConfig?, expectedVersion: String): Boolean {
        if (remoteConfig == null) {
            logger.warn("Remote config is null")
            return false
        }
        
        if (remoteConfig.version != expectedVersion) {
            logger.warn("Remote config version mismatch: expected=$expectedVersion, actual=${remoteConfig.version}")
            return false
        }
        
        if (remoteConfig.binaries.isEmpty()) {
            logger.warn("Remote config contains no binary download information")
            return false
        }
        
        // 验证每个二进制下载信息
        for ((arch, binaryInfo) in remoteConfig.binaries) {
            if (binaryInfo.downloadUrl.isBlank()) {
                logger.warn("Binary download URL is empty for architecture: $arch")
                return false
            }
            
            if (binaryInfo.version != expectedVersion) {
                logger.warn("Binary version mismatch for $arch: expected=$expectedVersion, actual=${binaryInfo.version}")
                return false
            }
        }
        
        logger.info("Remote config validation passed")
        return true
    }
    
    /**
     * 获取当前时间戳
     */
    private fun getCurrentTimestamp(): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        return dateFormat.format(Date())
    }
    
    /**
     * 获取remote.json文件路径
     */
    fun getRemoteConfigPath(resourcesDir: File): String {
        return File(resourcesDir, "remote.json").absolutePath
    }
}

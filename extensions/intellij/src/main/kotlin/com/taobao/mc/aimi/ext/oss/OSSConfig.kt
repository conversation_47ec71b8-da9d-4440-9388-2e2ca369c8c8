package com.taobao.mc.aimi.ext.oss

/**
 * 阿里云OSS配置类
 */
data class OSSConfig(
    /**
     * OSS访问密钥ID
     */
    val accessKeyId: String,
    
    /**
     * OSS访问密钥Secret
     */
    val accessKeySecret: String,
    
    /**
     * OSS Endpoint
     */
    val endpoint: String,
    
    /**
     * OSS Bucket名称
     */
    val bucketName: String,
    
    /**
     * 二进制文件存储路径前缀
     */
    val binaryPathPrefix: String = "aimi-binary",
    
    /**
     * CDN域名（用于下载）
     */
    val cdnDomain: String? = null
) {
    companion object {
        /**
         * 默认OSS配置
         */
        fun getDefault(): OSSConfig {
            return OSSConfig(
                accessKeyId = System.getProperty("aimi.oss.accessKeyId", ""),
                accessKeySecret = System.getProperty("aimi.oss.accessKeySecret", ""),
                endpoint = System.getProperty("aimi.oss.endpoint", "https://oss-cn-zhangjiakou.aliyuncs.com"),
                bucketName = System.getProperty("aimi.oss.bucketName", "aimi-ide"),
                binaryPathPrefix = System.getProperty("aimi.oss.binaryPathPrefix", "aimi-binary"),
                cdnDomain = System.getProperty("aimi.oss.cdnDomain", "https://aimi-ide.oss-cn-zhangjiakou.aliyuncs.com")
            )
        }
    }
}

package com.taobao.mc.aimi.ext.oss

import com.taobao.mc.aimi.ext.model.ObjectInfo
import com.taobao.mc.aimi.logger.LoggerManager
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.File
import java.io.FileOutputStream
import java.net.URI
import java.time.Instant
import java.util.concurrent.TimeUnit

/**
 * 运行时OSS下载器
 * 使用轻量级HTTP客户端实现下载功能
 */
class RuntimeOSSDownloader {
    private val logger = LoggerManager.getLogger(javaClass)
    
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(120, TimeUnit.SECONDS)
        .build()

    /**
     * 从CDN URL下载文件
     * @param url CDN URL
     * @param targetFile 目标文件
     * @param progressCallback 进度回调(已下载字节数, 总字节数)
     * @return 是否下载成功
     */
    fun downloadFromUrl(
        url: String,
        targetFile: File,
        progressCallback: (Long, <PERSON>) -> Unit = { _, _ -> }
    ): Bo<PERSON>an {
        return try {
            logger.info("Downloading from: $url")
            
            val request = Request.Builder().url(url).build()
            val response = httpClient.newCall(request).execute()

            if (!response.isSuccessful) {
                logger.error("Download failed with code: ${response.code}")
                return false
            }

            val body = response.body ?: return false
            val contentLength = body.contentLength()
            
            targetFile.parentFile?.mkdirs()
            
            FileOutputStream(targetFile).use { output ->
                body.byteStream().use { input ->
                    val buffer = ByteArray(8192)
                    var bytesRead: Int
                    var totalBytesRead = 0L

                    while (input.read(buffer).also { bytesRead = it } != -1) {
                        output.write(buffer, 0, bytesRead)
                        totalBytesRead += bytesRead
                        progressCallback(totalBytesRead, contentLength)
                    }
                }
            }
            
            logger.info("Downloaded successfully: ${targetFile.absolutePath}")
            true
        } catch (e: Exception) {
            logger.error("Download failed", e)
            false
        }
    }

    /**
     * 从CDN URL提取OSS对象键
     * @param cdnUrl CDN URL
     * @return OSS对象键
     */
    fun extractObjectKey(cdnUrl: String): String {
        return try {
            val uri = URI(cdnUrl)
            uri.path.removePrefix("/")
        } catch (e: Exception) {
            throw IllegalArgumentException("Invalid CDN URL: $cdnUrl", e)
        }
    }

    /**
     * 生成OSS对象键
     * @param version 版本号
     * @param arch 架构
     * @return OSS对象键
     */
    fun generateObjectKey(version: String, arch: String): String {
        return "aimi-plugin/core/${version}/${arch}/core.zip"
    }

    /**
     * 获取对象信息（简化版本）
     */
    fun getObjectInfo(url: String): ObjectInfo? {
        return try {
            val request = Request.Builder()
                .url(url)
                .head() // 只获取头部信息
                .build()
            
            val response = httpClient.newCall(request).execute()
            if (!response.isSuccessful) return null
            
            val contentLength = response.header("Content-Length")?.toLongOrNull() ?: 0L
            val etag = response.header("ETag") ?: ""
            val lastModified = response.header("Last-Modified")?.let { 
                try {
                    // 简化处理，使用当前时间
                    Instant.now()
                } catch (e: Exception) {
                    Instant.now()
                }
            } ?: Instant.now()
            
            ObjectInfo(
                key = extractObjectKey(url),
                size = contentLength,
                lastModified = lastModified,
                etag = etag
            )
        } catch (e: Exception) {
            logger.error("Failed to get object info for: $url", e)
            null
        }
    }
}
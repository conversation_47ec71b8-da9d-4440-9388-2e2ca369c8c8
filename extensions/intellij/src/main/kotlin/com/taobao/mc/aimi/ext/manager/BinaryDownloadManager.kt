package com.taobao.mc.aimi.ext.manager

import com.intellij.openapi.progress.ProgressIndicator
import com.intellij.openapi.progress.Task
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.ext.oss.RuntimeOSSDownloader
import com.taobao.mc.aimi.ext.util.RemoteConfigReader
import com.taobao.mc.aimi.ext.util.VersionInfoReader
import com.taobao.mc.aimi.ext.utils.getAIMIBinaryPath
import com.taobao.mc.aimi.ext.utils.getAIMICorePath
import com.taobao.mc.aimi.ext.utils.getOsAndArchTarget
import com.taobao.mc.aimi.logger.LoggerManager
import java.io.File
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit
import java.util.zip.ZipInputStream
import java.io.FileInputStream
import java.io.FileOutputStream

/**
 * 二进制下载管理器
 */
class BinaryDownloadManager {
    private val logger = LoggerManager.getLogger(javaClass)
    private val downloader = RuntimeOSSDownloader()

    /**
     * 校验并下载二进制文件（如果需要）
     * @param project 当前项目
     * @return 是否成功
     */
    fun validateAndDownload(project: Project?): Boolean {
        val binaryPath = getAIMIBinaryPath()
        val binaryFile = File(binaryPath)
        val currentPluginVersion = VersionInfoReader.getCurrentPluginVersion()

        logger.info("Validating binary: $binaryPath")
        logger.info("Current plugin version: $currentPluginVersion")

        // 检查是否为占位符文件或不存在
        if (!binaryFile.exists() || binaryFile.length() == 0L) {
            logger.info("Placeholder file detected, downloading binary...")
            return downloadBinaryFromRemote(project, currentPluginVersion)
        }

        // 检查版本是否匹配
        if (!VersionInfoReader.isVersionMatched(binaryFile, currentPluginVersion)) {
            logger.info("Version mismatch detected, downloading binary...")
            return downloadBinaryFromRemote(project, currentPluginVersion)
        }

        logger.info("Binary version matches, no download needed")
        return true
    }

    /**
     * 从远程下载二进制文件
     */
    private fun downloadBinaryFromRemote(project: Project?, pluginVersion: String): Boolean {
        val architecture = getOsAndArchTarget()
        logger.info("Downloading binary for architecture: $architecture")

        val archInfo = RemoteConfigReader.getArchitectureInfo(architecture)
        if (archInfo == null) {
            logger.error("No download info found for architecture: $architecture")
            return false
        }

        if (project == null) {
            logger.warn("No project available for background download, downloading synchronously")
            return downloadSynchronously(archInfo.url, architecture)
        }

        // 在后台任务中执行下载
        var downloadSuccess = false
        val latch = CountDownLatch(1)

        val task = object : Task.Backgroundable(project, "下载AIMI核心组件", true) {
            override fun run(indicator: ProgressIndicator) {
                try {
                    indicator.text = "正在下载AIMI核心组件..."
                    indicator.isIndeterminate = false
                    
                    downloadSuccess = performDownload(archInfo.url, architecture) { downloaded, total ->
                        if (total > 0) {
                            indicator.fraction = downloaded.toDouble() / total
                            indicator.text2 = "${downloaded / 1024 / 1024}MB / ${total / 1024 / 1024}MB"
                        }
                    }
                } finally {
                    latch.countDown()
                }
            }
        }

        task.queue()
        
        // 等待下载完成（最多120秒）
        return latch.await(120, TimeUnit.SECONDS) && downloadSuccess
    }

    /**
     * 同步下载（用于没有project的情况）
     */
    private fun downloadSynchronously(url: String, architecture: String): Boolean {
        return performDownload(url, architecture) { downloaded, total ->
            if (total > 0) {
                val percent = (downloaded * 100 / total).toInt()
                logger.info("Download progress: $percent% (${downloaded / 1024 / 1024}MB / ${total / 1024 / 1024}MB)")
            }
        }
    }

    /**
     * 执行下载操作
     */
    private fun performDownload(
        url: String, 
        architecture: String,
        progressCallback: (Long, Long) -> Unit = { _, _ -> }
    ): Boolean {
        return try {
            val tempFile = File.createTempFile("aimi-core-", ".zip")
            tempFile.deleteOnExit()

            logger.info("Downloading from: $url")
            
            // 下载文件
            if (!downloader.downloadFromUrl(url, tempFile, progressCallback)) {
                logger.error("Failed to download binary")
                return false
            }

            // 清空并解压到core目录
            val coreDir = File(getAIMICorePath()).parentFile // 获取core目录而不是架构子目录
            logger.info("Extracting to: ${coreDir.absolutePath}")
            
            if (!extractZipToCore(tempFile, coreDir, architecture)) {
                logger.error("Failed to extract binary")
                return false
            }

            logger.info("Binary download and extraction completed successfully")
            true
        } catch (e: Exception) {
            logger.error("Download failed", e)
            false
        }
    }

    /**
     * 解压ZIP文件到core目录
     */
    private fun extractZipToCore(zipFile: File, coreDir: File, architecture: String): Boolean {
        return try {
            // 创建架构目录
            val archDir = File(coreDir, architecture)
            if (archDir.exists()) {
                archDir.deleteRecursively()
            }
            archDir.mkdirs()

            logger.info("Extracting ZIP to: ${archDir.absolutePath}")

            ZipInputStream(FileInputStream(zipFile)).use { zipIn ->
                var entry = zipIn.nextEntry
                while (entry != null) {
                    val file = File(archDir, entry.name)
                    
                    if (entry.isDirectory) {
                        file.mkdirs()
                    } else {
                        file.parentFile?.mkdirs()
                        FileOutputStream(file).use { output ->
                            zipIn.copyTo(output)
                        }
                        
                        // 设置可执行权限（如果是二进制文件）
                        if (file.name.contains("aimi-binary") && !file.name.endsWith(".exe")) {
                            file.setExecutable(true)
                            logger.info("Set executable permission for: ${file.name}")
                        }
                    }
                    
                    zipIn.closeEntry()
                    entry = zipIn.nextEntry
                }
            }
            
            logger.info("Extraction completed successfully")
            true
        } catch (e: Exception) {
            logger.error("Failed to extract ZIP", e)
            false
        }
    }
}
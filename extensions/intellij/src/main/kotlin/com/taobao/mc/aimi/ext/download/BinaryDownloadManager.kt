package com.taobao.mc.aimi.ext.download

import com.intellij.openapi.progress.ProgressIndicator
import com.intellij.openapi.progress.ProgressManager
import com.intellij.openapi.progress.Task
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.ext.config.RemoteConfig
import com.taobao.mc.aimi.ext.config.RemoteConfigGenerator
import com.taobao.mc.aimi.ext.utils.ErrorHandler
import com.taobao.mc.aimi.ext.utils.getAIMICorePath
import com.taobao.mc.aimi.ext.utils.getOsAndArchTarget
import com.taobao.mc.aimi.ext.version.ValidationResult
import com.taobao.mc.aimi.ext.version.VersionManager
import com.taobao.mc.aimi.logger.LoggerManager
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.util.concurrent.TimeUnit
import java.util.zip.ZipEntry
import java.util.zip.ZipInputStream

/**
 * 二进制文件下载管理器
 * 负责从CDN下载、解压二进制文件
 */
class BinaryDownloadManager(
    private val project: Project,
    private val versionManager: VersionManager = VersionManager()
) {
    
    private val logger = LoggerManager.getLogger(javaClass)
    private val configGenerator = RemoteConfigGenerator()
    
    /**
     * 检查并下载二进制文件
     * 
     * @param expectedPluginVersion 期望的插件版本
     * @return 检查和下载是否成功
     */
    fun checkAndDownloadBinaries(expectedPluginVersion: String): Boolean {
        try {
            logger.info("Checking binary version for plugin version: $expectedPluginVersion")
            
            // 1. 校验本地二进制文件版本
            val validationResult = versionManager.validateLocalBinaryVersion(expectedPluginVersion)
            
            when (validationResult) {
                ValidationResult.VALID -> {
                    logger.info("Local binary version is valid, no download needed")
                    return true
                }
                ValidationResult.MISSING,
                ValidationResult.VERSION_MISMATCH,
                ValidationResult.PLATFORM_MISMATCH -> {
                    logger.info("Local binary validation failed: $validationResult, will download from CDN")
                    return downloadAndInstallBinaries(expectedPluginVersion)
                }
                ValidationResult.ERROR -> {
                    logger.error("Binary validation error, will attempt to download")
                    return downloadAndInstallBinaries(expectedPluginVersion)
                }
            }
            
        } catch (e: Exception) {
            logger.error("Failed to check and download binaries", e)
            return false
        }
    }
    
    /**
     * 从CDN下载并安装二进制文件
     */
    private fun downloadAndInstallBinaries(pluginVersion: String): Boolean {
        return try {
            // 1. 读取remote.json配置
            val remoteConfig = loadRemoteConfig() ?: return false
            
            // 2. 验证配置有效性
            if (!configGenerator.validateRemoteConfig(remoteConfig, pluginVersion)) {
                logger.error("Remote config validation failed")
                return false
            }
            
            // 3. 获取当前平台的下载信息
            val currentPlatform = getOsAndArchTarget()
            val downloadInfo = remoteConfig.getBinaryInfo(currentPlatform)
            
            if (downloadInfo == null) {
                logger.error("No download information found for platform: $currentPlatform")
                return false
            }
            
            // 4. 在后台任务中执行下载
            var downloadSuccess = false
            
            ProgressManager.getInstance().run(object : Task.Backgroundable(project, "Downloading AIMI Binary Files", true) {
                override fun run(indicator: ProgressIndicator) {
                    downloadSuccess = performDownload(downloadInfo, indicator)
                }
            })
            
            downloadSuccess
            
        } catch (e: Exception) {
            logger.error("Failed to download and install binaries", e)
            false
        }
    }
    
    /**
     * 执行实际的下载操作
     */
    private fun performDownload(downloadInfo: com.taobao.mc.aimi.ext.config.BinaryDownloadInfo, indicator: ProgressIndicator): Boolean {
        try {
            indicator.text = "Preparing to download binary files..."
            indicator.fraction = 0.0
            
            // 1. 清空core目录
            indicator.text = "Clearing core directory..."
            if (!versionManager.clearCoreDirectory()) {
                logger.error("Failed to clear core directory")
                return false
            }
            indicator.fraction = 0.1
            
            // 2. 下载压缩包
            indicator.text = "Downloading binary archive..."
            val downloadedFile = downloadFile(downloadInfo.downloadUrl, indicator) ?: return false
            indicator.fraction = 0.7
            
            try {
                // 3. 解压到core目录
                indicator.text = "Extracting binary files..."
                val coreDir = File(getAIMICorePath()).parentFile // 获取core的父目录
                if (!extractZipFile(downloadedFile, coreDir, indicator)) {
                    logger.error("Failed to extract binary files")
                    return false
                }
                indicator.fraction = 0.9
                
                // 4. 验证解压结果
                indicator.text = "Validating extracted files..."
                val validationResult = versionManager.validateLocalBinaryVersion(downloadInfo.version)
                if (validationResult != ValidationResult.VALID) {
                    logger.error("Downloaded binary validation failed: $validationResult")
                    return false
                }
                
                indicator.text = "Binary download completed successfully"
                indicator.fraction = 1.0
                
                logger.info("Binary files downloaded and installed successfully")
                return true
                
            } finally {
                // 清理下载的临时文件
                downloadedFile.delete()
            }
            
        } catch (e: Exception) {
            logger.error("Failed to perform download", e)
            return false
        }
    }
    
    /**
     * 下载文件
     */
    private fun downloadFile(url: String, indicator: ProgressIndicator): File? {
        try {
            val client = OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .build()
            
            val request = Request.Builder().url(url).build()
            val response = client.newCall(request).execute()
            
            if (!response.isSuccessful) {
                logger.error("Download failed with HTTP code: ${response.code}")
                return null
            }
            
            val contentLength = response.body?.contentLength() ?: -1
            val tempFile = File.createTempFile("aimi-binary-", ".zip")
            
            response.body?.byteStream()?.use { input ->
                FileOutputStream(tempFile).use { output ->
                    val buffer = ByteArray(8192)
                    var totalBytesRead = 0L
                    var bytesRead: Int
                    
                    while (input.read(buffer).also { bytesRead = it } != -1) {
                        if (indicator.isCanceled) {
                            tempFile.delete()
                            return null
                        }
                        
                        output.write(buffer, 0, bytesRead)
                        totalBytesRead += bytesRead
                        
                        if (contentLength > 0) {
                            val progress = 0.1 + (totalBytesRead.toDouble() / contentLength) * 0.6
                            indicator.fraction = progress
                            indicator.text = "Downloaded ${totalBytesRead / 1024 / 1024} MB of ${contentLength / 1024 / 1024} MB"
                        }
                    }
                }
            }
            
            logger.info("File downloaded successfully: ${tempFile.absolutePath} (${tempFile.length()} bytes)")
            return tempFile
            
        } catch (e: Exception) {
            logger.error("Failed to download file from $url", e)
            return null
        }
    }
    
    /**
     * 解压ZIP文件
     */
    private fun extractZipFile(zipFile: File, targetDir: File, indicator: ProgressIndicator): Boolean {
        try {
            ZipInputStream(FileInputStream(zipFile)).use { zipIn ->
                var entry: ZipEntry? = zipIn.nextEntry
                
                while (entry != null) {
                    if (indicator.isCanceled) {
                        return false
                    }
                    
                    val filePath = File(targetDir, entry.name)
                    
                    if (entry.isDirectory) {
                        filePath.mkdirs()
                    } else {
                        // 确保父目录存在
                        filePath.parentFile?.mkdirs()
                        
                        // 解压文件
                        FileOutputStream(filePath).use { output ->
                            zipIn.copyTo(output)
                        }
                        
                        // 设置可执行权限（如果是可执行文件）
                        if (filePath.name.contains("aimi-binary") || filePath.name == "rg") {
                            filePath.setExecutable(true)
                        }
                    }
                    
                    zipIn.closeEntry()
                    entry = zipIn.nextEntry
                }
            }
            
            logger.info("ZIP file extracted successfully to: ${targetDir.absolutePath}")
            return true
            
        } catch (e: Exception) {
            logger.error("Failed to extract ZIP file", e)
            return false
        }
    }
    
    /**
     * 加载远程配置
     */
    private fun loadRemoteConfig(): RemoteConfig? {
        try {
            // 从插件resources目录读取remote.json
            val resourceStream = javaClass.classLoader.getResourceAsStream("remote.json")
            if (resourceStream == null) {
                logger.error("remote.json not found in plugin resources")
                return null
            }
            
            val jsonContent = resourceStream.bufferedReader().use { it.readText() }
            return RemoteConfig.fromJson(jsonContent)
            
        } catch (e: Exception) {
            logger.error("Failed to load remote config", e)
            return null
        }
    }
}

package com.taobao.mc.aimi.ext.oss

import com.aliyun.oss.OSS
import com.aliyun.oss.OSSClientBuilder
import com.aliyun.oss.model.ObjectMetadata
import com.aliyun.oss.model.PutObjectRequest
import com.taobao.mc.aimi.logger.LoggerManager
import java.io.File
import java.io.FileInputStream
import java.io.InputStream
import java.util.*

/**
 * 阿里云OSS上传服务
 */
class OSSUploadService(private val config: OSSConfig) {
    
    private val logger = LoggerManager.getLogger(javaClass)
    
    /**
     * 上传文件到OSS
     * 
     * @param localFile 本地文件
     * @param objectKey OSS对象键
     * @param metadata 自定义元数据
     * @return 上传成功返回true，失败返回false
     */
    fun uploadFile(localFile: File, objectKey: String, metadata: Map<String, String> = emptyMap()): Boolean {
        if (!localFile.exists()) {
            logger.error("Local file does not exist: ${localFile.absolutePath}")
            return false
        }
        
        var ossClient: OSS? = null
        var inputStream: InputStream? = null
        
        try {
            // 创建OSS客户端
            ossClient = OSSClientBuilder().build(config.endpoint, config.accessKeyId, config.accessKeySecret)
            
            // 创建文件输入流
            inputStream = FileInputStream(localFile)
            
            // 创建对象元数据
            val objectMetadata = ObjectMetadata().apply {
                contentLength = localFile.length()
                contentType = getContentType(localFile.name)
                
                // 添加自定义元数据
                metadata.forEach { (key, value) ->
                    addUserMetadata(key, value)
                }
            }
            
            // 创建上传请求
            val putObjectRequest = PutObjectRequest(config.bucketName, objectKey, inputStream, objectMetadata)
            
            // 执行上传
            val result = ossClient.putObject(putObjectRequest)
            
            logger.info("File uploaded successfully: $objectKey, ETag: ${result.eTag}")
            return true
            
        } catch (e: Exception) {
            logger.error("Failed to upload file to OSS: $objectKey", e)
            return false
        } finally {
            // 关闭资源
            inputStream?.close()
            ossClient?.shutdown()
        }
    }
    
    /**
     * 批量上传文件
     * 
     * @param files 文件映射，key为OSS对象键，value为本地文件
     * @param commonMetadata 公共元数据
     * @return 上传结果映射，key为OSS对象键，value为上传是否成功
     */
    fun uploadFiles(files: Map<String, File>, commonMetadata: Map<String, String> = emptyMap()): Map<String, Boolean> {
        val results = mutableMapOf<String, Boolean>()
        
        files.forEach { (objectKey, localFile) ->
            results[objectKey] = uploadFile(localFile, objectKey, commonMetadata)
        }
        
        return results
    }
    
    /**
     * 获取文件的Content-Type
     */
    private fun getContentType(fileName: String): String {
        return when (fileName.substringAfterLast('.').lowercase()) {
            "zip" -> "application/zip"
            "tar" -> "application/x-tar"
            "gz" -> "application/gzip"
            "json" -> "application/json"
            "exe" -> "application/octet-stream"
            else -> "application/octet-stream"
        }
    }
    
    /**
     * 生成下载URL
     * 
     * @param objectKey OSS对象键
     * @return 下载URL
     */
    fun getDownloadUrl(objectKey: String): String {
        return if (config.cdnDomain != null) {
            "${config.cdnDomain.trimEnd('/')}/$objectKey"
        } else {
            "https://${config.bucketName}.${config.endpoint.removePrefix("https://").removePrefix("http://")}/$objectKey"
        }
    }
    
    /**
     * 检查文件是否存在
     * 
     * @param objectKey OSS对象键
     * @return 文件是否存在
     */
    fun doesObjectExist(objectKey: String): Boolean {
        var ossClient: OSS? = null
        
        try {
            ossClient = OSSClientBuilder().build(config.endpoint, config.accessKeyId, config.accessKeySecret)
            return ossClient.doesObjectExist(config.bucketName, objectKey)
        } catch (e: Exception) {
            logger.error("Failed to check object existence: $objectKey", e)
            return false
        } finally {
            ossClient?.shutdown()
        }
    }
}

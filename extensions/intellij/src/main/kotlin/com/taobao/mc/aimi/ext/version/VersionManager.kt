package com.taobao.mc.aimi.ext.version

import com.taobao.mc.aimi.ext.utils.getAIMICorePath
import com.taobao.mc.aimi.ext.utils.getOsAndArchTarget
import com.taobao.mc.aimi.logger.LoggerManager
import java.io.File
import java.nio.file.Files
import java.nio.file.Paths

/**
 * 版本管理器
 * 负责版本信息的创建、读取、校验等操作
 */
class VersionManager {
    
    private val logger = LoggerManager.getLogger(javaClass)
    
    companion object {
        private const val VERSION_FILE_NAME = "version.json"
    }
    
    /**
     * 创建版本信息文件
     * 
     * @param targetDir 目标目录
     * @param pluginVersion 插件版本
     * @param gitCommitHash Git提交哈希（可选）
     * @return 创建是否成功
     */
    fun createVersionFile(targetDir: File, pluginVersion: String, gitCommitHash: String? = null): Boolean {
        try {
            val targetPlatform = getOsAndArchTarget()
            val versionInfo = VersionInfo.createCurrent(pluginVersion, targetPlatform, gitCommitHash)
            
            val versionFile = File(targetDir, VERSION_FILE_NAME)
            versionFile.writeText(versionInfo.toJson())
            
            logger.info("Version file created: ${versionFile.absolutePath}")
            logger.info("Version info: ${versionInfo.getSummary()}")
            
            return true
        } catch (e: Exception) {
            logger.error("Failed to create version file in $targetDir", e)
            return false
        }
    }
    
    /**
     * 读取版本信息文件
     * 
     * @param sourceDir 源目录
     * @return 版本信息，如果读取失败返回null
     */
    fun readVersionFile(sourceDir: File): VersionInfo? {
        try {
            val versionFile = File(sourceDir, VERSION_FILE_NAME)
            if (!versionFile.exists()) {
                logger.warn("Version file not found: ${versionFile.absolutePath}")
                return null
            }
            
            val jsonContent = versionFile.readText()
            val versionInfo = VersionInfo.fromJson(jsonContent)
            
            if (versionInfo != null) {
                logger.info("Version file read successfully: ${versionInfo.getSummary()}")
            } else {
                logger.error("Failed to parse version file: ${versionFile.absolutePath}")
            }
            
            return versionInfo
        } catch (e: Exception) {
            logger.error("Failed to read version file from $sourceDir", e)
            return null
        }
    }
    
    /**
     * 校验本地二进制文件版本
     * 
     * @param expectedPluginVersion 期望的插件版本
     * @return 校验结果
     */
    fun validateLocalBinaryVersion(expectedPluginVersion: String): ValidationResult {
        try {
            val corePath = getAIMICorePath()
            val coreDir = File(corePath)
            
            if (!coreDir.exists()) {
                logger.info("Core directory does not exist: $corePath")
                return ValidationResult.MISSING
            }
            
            val versionInfo = readVersionFile(coreDir)
            if (versionInfo == null) {
                logger.info("Version file not found in core directory")
                return ValidationResult.MISSING
            }
            
            val currentPlatform = getOsAndArchTarget()
            
            // 检查版本匹配
            if (!versionInfo.isVersionMatch(expectedPluginVersion)) {
                logger.info("Version mismatch: expected=$expectedPluginVersion, actual=${versionInfo.pluginVersion}")
                return ValidationResult.VERSION_MISMATCH
            }
            
            // 检查平台匹配
            if (!versionInfo.isPlatformMatch(currentPlatform)) {
                logger.info("Platform mismatch: expected=$currentPlatform, actual=${versionInfo.targetPlatform}")
                return ValidationResult.PLATFORM_MISMATCH
            }
            
            logger.info("Binary version validation passed: ${versionInfo.getSummary()}")
            return ValidationResult.VALID
            
        } catch (e: Exception) {
            logger.error("Failed to validate local binary version", e)
            return ValidationResult.ERROR
        }
    }
    
    /**
     * 清空core目录
     * 
     * @return 清空是否成功
     */
    fun clearCoreDirectory(): Boolean {
        try {
            val corePath = getAIMICorePath()
            val coreDir = File(corePath)
            
            if (coreDir.exists()) {
                coreDir.deleteRecursively()
                logger.info("Core directory cleared: $corePath")
            }
            
            // 重新创建目录
            coreDir.mkdirs()
            return true
            
        } catch (e: Exception) {
            logger.error("Failed to clear core directory", e)
            return false
        }
    }
    
    /**
     * 获取版本文件路径
     */
    fun getVersionFilePath(directory: File): String {
        return File(directory, VERSION_FILE_NAME).absolutePath
    }
}

/**
 * 版本校验结果
 */
enum class ValidationResult {
    /**
     * 版本有效
     */
    VALID,
    
    /**
     * 文件缺失
     */
    MISSING,
    
    /**
     * 版本不匹配
     */
    VERSION_MISMATCH,
    
    /**
     * 平台不匹配
     */
    PLATFORM_MISMATCH,
    
    /**
     * 校验错误
     */
    ERROR
}

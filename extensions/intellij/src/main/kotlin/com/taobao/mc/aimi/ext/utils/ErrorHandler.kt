package com.taobao.mc.aimi.ext.utils

import com.intellij.notification.NotificationGroupManager
import com.intellij.notification.NotificationType
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.logger.LoggerManager

/**
 * 统一的错误处理工具类
 */
object ErrorHandler {
    
    private val logger = LoggerManager.getLogger(ErrorHandler::class.java)
    
    /**
     * 处理二进制文件相关的错误
     */
    fun handleBinaryError(
        project: Project?,
        operation: String,
        error: Throwable,
        showNotification: Boolean = true
    ) {
        val message = "Binary operation '$operation' failed: ${error.message}"
        logger.error(message, error)
        
        if (showNotification && project != null) {
            showErrorNotification(
                project,
                "AIMI Binary Error",
                "Failed to $operation. Please check the logs for details."
            )
        }
    }
    
    /**
     * 处理OSS上传相关的错误
     */
    fun handleOSSError(
        project: Project?,
        operation: String,
        error: Throwable,
        showNotification: Boolean = false
    ) {
        val message = "OSS operation '$operation' failed: ${error.message}"
        logger.error(message, error)
        
        if (showNotification && project != null) {
            showErrorNotification(
                project,
                "AIMI Upload Error",
                "Failed to $operation. This may affect future updates."
            )
        }
    }
    
    /**
     * 处理下载相关的错误
     */
    fun handleDownloadError(
        project: Project?,
        operation: String,
        error: Throwable,
        showNotification: Boolean = true
    ) {
        val message = "Download operation '$operation' failed: ${error.message}"
        logger.error(message, error)
        
        if (showNotification && project != null) {
            showErrorNotification(
                project,
                "AIMI Download Error",
                "Failed to $operation. Please check your network connection and try again."
            )
        }
    }
    
    /**
     * 处理版本校验相关的错误
     */
    fun handleVersionError(
        project: Project?,
        operation: String,
        error: Throwable,
        showNotification: Boolean = true
    ) {
        val message = "Version operation '$operation' failed: ${error.message}"
        logger.error(message, error)
        
        if (showNotification && project != null) {
            showErrorNotification(
                project,
                "AIMI Version Error",
                "Failed to $operation. Binary files may be outdated."
            )
        }
    }
    
    /**
     * 记录操作成功的信息
     */
    fun logSuccess(operation: String, details: String = "") {
        val message = if (details.isNotEmpty()) {
            "Operation '$operation' completed successfully: $details"
        } else {
            "Operation '$operation' completed successfully"
        }
        logger.info(message)
    }
    
    /**
     * 记录操作开始的信息
     */
    fun logStart(operation: String, details: String = "") {
        val message = if (details.isNotEmpty()) {
            "Starting operation '$operation': $details"
        } else {
            "Starting operation '$operation'"
        }
        logger.info(message)
    }
    
    /**
     * 记录操作进度的信息
     */
    fun logProgress(operation: String, progress: String) {
        logger.info("Operation '$operation' progress: $progress")
    }
    
    /**
     * 显示错误通知
     */
    private fun showErrorNotification(
        project: Project,
        title: String,
        content: String
    ) {
        try {
            NotificationGroupManager.getInstance()
                .getNotificationGroup("AIMI")
                .createNotification(title, content, NotificationType.ERROR)
                .notify(project)
        } catch (e: Exception) {
            logger.warn("Failed to show error notification", e)
        }
    }
    
    /**
     * 显示信息通知
     */
    fun showInfoNotification(
        project: Project,
        title: String,
        content: String
    ) {
        try {
            NotificationGroupManager.getInstance()
                .getNotificationGroup("AIMI")
                .createNotification(title, content, NotificationType.INFORMATION)
                .notify(project)
        } catch (e: Exception) {
            logger.warn("Failed to show info notification", e)
        }
    }
    
    /**
     * 显示警告通知
     */
    fun showWarningNotification(
        project: Project,
        title: String,
        content: String
    ) {
        try {
            NotificationGroupManager.getInstance()
                .getNotificationGroup("AIMI")
                .createNotification(title, content, NotificationType.WARNING)
                .notify(project)
        } catch (e: Exception) {
            logger.warn("Failed to show warning notification", e)
        }
    }
    
    /**
     * 安全执行操作，自动处理异常
     */
    inline fun <T> safeExecute(
        project: Project?,
        operation: String,
        showNotification: Boolean = true,
        action: () -> T
    ): T? {
        return try {
            logStart(operation)
            val result = action()
            logSuccess(operation)
            result
        } catch (e: Exception) {
            handleBinaryError(project, operation, e, showNotification)
            null
        }
    }
}

package com.taobao.mc.aimi.ext.gradle

import com.taobao.mc.aimi.ext.config.RemoteConfigGenerator
import com.taobao.mc.aimi.ext.oss.BinaryUploadManager
import com.taobao.mc.aimi.ext.oss.OSSConfig
import com.taobao.mc.aimi.ext.version.VersionManager
import org.gradle.api.DefaultTask
import org.gradle.api.provider.Property
import org.gradle.api.tasks.Input
import org.gradle.api.tasks.InputDirectory
import org.gradle.api.tasks.Optional
import org.gradle.api.tasks.TaskAction
import java.io.File

/**
 * 二进制文件上传Gradle任务
 */
abstract class BinaryUploadTask : DefaultTask() {
    
    @get:InputDirectory
    abstract val coreDirectory: Property<File>
    
    @get:Input
    abstract val pluginVersion: Property<String>
    
    @get:Input
    @get:Optional
    abstract val gitCommitHash: Property<String>
    
    @get:Input
    @get:Optional
    abstract val ossAccessKeyId: Property<String>
    
    @get:Input
    @get:Optional
    abstract val ossAccessKeySecret: Property<String>
    
    @get:Input
    @get:Optional
    abstract val ossEndpoint: Property<String>
    
    @get:Input
    @get:Optional
    abstract val ossBucketName: Property<String>

    @get:InputDirectory
    @get:Optional
    abstract val resourcesDirectory: Property<File>

    init {
        description = "Upload binary files to OSS CDN"
        group = "aimi"
    }
    
    @TaskAction
    fun uploadBinaries() {
        val coreDir = coreDirectory.get()
        val version = pluginVersion.get()
        
        logger.lifecycle("Starting binary upload for version: $version")
        logger.lifecycle("Core directory: ${coreDir.absolutePath}")
        
        // 检查是否启用OSS上传
        if (!isOSSUploadEnabled()) {
            logger.lifecycle("OSS upload is disabled. Skipping binary upload.")
            return
        }
        
        try {
            // 创建OSS配置
            val ossConfig = createOSSConfig()
            
            // 创建上传管理器
            val uploadManager = BinaryUploadManager(ossConfig)
            
            // 执行上传
            val result = uploadManager.compressAndUpload(
                coreDirectory = coreDir,
                pluginVersion = version,
                gitCommitHash = gitCommitHash.orNull
            )
            
            if (result.success) {
                logger.lifecycle("Binary upload completed successfully: ${result.message}")
                
                // 输出下载URL
                result.downloadUrls.forEach { (arch, url) ->
                    logger.lifecycle("  $arch: $url")
                }
                
                // 生成remote.json配置文件
                generateRemoteConfig(result, version)

                // 创建占位文件
                createPlaceholderFiles(coreDir)

                if (result.isPartial) {
                    logger.warn("Upload was only partially successful")
                }
                
            } else {
                logger.error("Binary upload failed: ${result.message}")
                throw RuntimeException("Binary upload failed: ${result.message}")
            }
            
        } catch (e: Exception) {
            logger.error("Failed to upload binaries", e)
            throw e
        }
    }
    
    /**
     * 检查是否启用OSS上传
     */
    private fun isOSSUploadEnabled(): Boolean {
        // 检查必要的OSS配置是否存在
        return ossAccessKeyId.isPresent && 
               ossAccessKeySecret.isPresent && 
               ossEndpoint.isPresent && 
               ossBucketName.isPresent &&
               ossAccessKeyId.get().isNotEmpty() &&
               ossAccessKeySecret.get().isNotEmpty()
    }
    
    /**
     * 创建OSS配置
     */
    private fun createOSSConfig(): OSSConfig {
        return OSSConfig(
            accessKeyId = ossAccessKeyId.get(),
            accessKeySecret = ossAccessKeySecret.get(),
            endpoint = ossEndpoint.get(),
            bucketName = ossBucketName.get(),
            binaryPathPrefix = "aimi-binary",
            cdnDomain = "https://${ossBucketName.get()}.${ossEndpoint.get().removePrefix("https://").removePrefix("http://")}"
        )
    }
    
    /**
     * 生成remote.json配置文件
     */
    private fun generateRemoteConfig(uploadResult: com.taobao.mc.aimi.ext.oss.UploadResult, version: String) {
        try {
            val resourcesDir = resourcesDirectory.orNull
            if (resourcesDir == null) {
                logger.warn("Resources directory not specified, skipping remote config generation")
                return
            }

            val configGenerator = RemoteConfigGenerator()
            val success = configGenerator.generateRemoteConfig(uploadResult, version, resourcesDir)

            if (success) {
                logger.lifecycle("Remote config file generated successfully")
            } else {
                logger.error("Failed to generate remote config file")
            }

        } catch (e: Exception) {
            logger.error("Failed to generate remote config", e)
        }
    }

    /**
     * 创建占位文件
     */
    private fun createPlaceholderFiles(coreDir: File) {
        try {
            logger.lifecycle("Creating placeholder files in core directory")
            
            // 获取所有架构目录
            val archDirectories = coreDir.listFiles { file ->
                file.isDirectory && file.name.matches(Regex("\\w+-\\w+"))
            } ?: return
            
            for (archDir in archDirectories) {
                // 获取目录中的所有文件
                val files = archDir.listFiles() ?: continue
                
                for (file in files) {
                    if (file.isFile) {
                        // 创建同名的0字节占位文件
                        val placeholderFile = File(archDir, file.name)
                        placeholderFile.writeText("")
                        logger.debug("Created placeholder file: ${placeholderFile.absolutePath}")
                    }
                }
            }
            
            logger.lifecycle("Placeholder files created successfully")
            
        } catch (e: Exception) {
            logger.warn("Failed to create placeholder files", e)
        }
    }
}

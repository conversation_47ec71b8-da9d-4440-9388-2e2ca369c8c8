package com.taobao.mc.aimi.ext.util

import com.google.gson.Gson
import com.taobao.mc.aimi.ext.model.RemoteConfig
import com.taobao.mc.aimi.logger.LoggerManager

/**
 * 远程配置读取器
 */
object RemoteConfigReader {
    
    private val gson = Gson()
    private val logger = LoggerManager.getLogger(RemoteConfigReader::class.java)

    /**
     * 从resources目录加载remote.json配置
     */
    fun loadConfig(): RemoteConfig? {
        return try {
            val resourceStream = javaClass.getResourceAsStream("/remote.json")
            if (resourceStream == null) {
                logger.warn("remote.json not found in resources")
                return null
            }
            
            val configJson = resourceStream.bufferedReader().use { it.readText() }
            val config = gson.fromJson(configJson, RemoteConfig::class.java)
            
            logger.info("Loaded remote config - version: ${config.version}, architectures: ${config.architectures.keys}")
            config
        } catch (e: Exception) {
            logger.error("Failed to load remote config", e)
            null
        }
    }

    /**
     * 获取指定架构的下载信息
     */
    fun getArchitectureInfo(architecture: String): com.taobao.mc.aimi.ext.model.ArchInfo? {
        val config = loadConfig() ?: return null
        return config.architectures[architecture]
    }
}
package com.taobao.mc.aimi.ext.util

import com.google.gson.Gson
import com.taobao.mc.aimi.ext.model.VersionInfo
import com.taobao.mc.aimi.logger.LoggerManager
import java.io.File
import java.io.RandomAccessFile
import java.security.MessageDigest

/**
 * 插件端版本信息读取器
 */
object VersionInfoReader {
    
    private const val VERSION_MAGIC = "AIMI_VERSION"
    private val gson = Gson()
    private val logger = LoggerManager.getLogger(VersionInfoReader::class.java)

    /**
     * 从二进制文件读取版本信息
     */
    fun readVersionInfo(binaryFile: File): VersionInfo? {
        return try {
            if (!binaryFile.exists() || binaryFile.length() == 0L) {
                logger.debug("Binary file does not exist or is empty: ${binaryFile.absolutePath}")
                return null
            }

            RandomAccessFile(binaryFile, "r").use { raf ->
                val fileLength = raf.length()
                val magicBytes = VERSION_MAGIC.toByteArray(Charsets.UTF_8)
                
                // 检查文件是否足够大来包含版本信息
                if (fileLength < magicBytes.size + 4) {
                    logger.debug("File too small to contain version info: ${binaryFile.absolutePath}")
                    return null
                }

                // 移动到魔术字符串位置
                raf.seek(fileLength - magicBytes.size)
                val readMagic = ByteArray(magicBytes.size)
                raf.readFully(readMagic)

                // 验证魔术字符串
                if (!readMagic.contentEquals(magicBytes)) {
                    logger.debug("Version magic not found in: ${binaryFile.absolutePath}")
                    return null
                }

                // 读取版本信息长度
                raf.seek(fileLength - magicBytes.size - 4)
                val versionLength = raf.readInt()
                
                if (versionLength <= 0 || versionLength > 1024) {
                    logger.debug("Invalid version length: $versionLength")
                    return null
                }

                // 读取版本信息
                raf.seek(fileLength - magicBytes.size - 4 - versionLength)
                val versionBytes = ByteArray(versionLength)
                raf.readFully(versionBytes)

                val versionJson = String(versionBytes, Charsets.UTF_8)
                logger.debug("Read version info: $versionJson")
                
                gson.fromJson(versionJson, VersionInfo::class.java)
            }
        } catch (e: Exception) {
            logger.warn("Failed to read version info from ${binaryFile.absolutePath}", e)
            null
        }
    }

    /**
     * 检查版本是否匹配
     */
    fun isVersionMatched(binaryFile: File, expectedVersion: String): Boolean {
        val versionInfo = readVersionInfo(binaryFile) ?: return false
        val matched = versionInfo.pluginVersion == expectedVersion
        logger.debug("Version match check: expected=$expectedVersion, actual=${versionInfo.pluginVersion}, matched=$matched")
        return matched
    }
    
    /**
     * 获取当前插件版本
     */
    fun getCurrentPluginVersion(): String {
        return try {
            // 从插件描述符获取版本信息
            val pluginDescriptor = com.intellij.ide.plugins.PluginManagerCore
                .getPlugin(com.intellij.openapi.extensions.PluginId.getId("com.taobao.mc.aimi"))
            pluginDescriptor?.version ?: "unknown"
        } catch (e: Exception) {
            logger.warn("Failed to get plugin version", e)
            "unknown"
        }
    }
}
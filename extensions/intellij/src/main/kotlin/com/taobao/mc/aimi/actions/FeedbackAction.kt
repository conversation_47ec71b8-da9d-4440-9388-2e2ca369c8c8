package com.taobao.mc.aimi.actions

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.project.DumbAware
import com.taobao.mc.aimi.ext.error.log.LogUploadService

/**
 * 上传日志Action
 * 用于在菜单或工具栏中触发日志上传功能
 */
class FeedbackAction : AnAction(), DumbAware {

    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project
        LogUploadService.getInstance().startUpload(project)
    }

    override fun update(e: AnActionEvent) {
        // 始终启用此操作
        e.presentation.isEnabledAndVisible = true
    }
}
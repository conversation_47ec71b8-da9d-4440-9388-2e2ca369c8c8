package com.taobao.mc.aimi.ext.model

import java.time.Instant

/**
 * 架构信息
 */
data class ArchInfo(
    val url: String,
    val size: Long,
    val checksum: String? = null
)

/**
 * 远程配置信息
 */
data class RemoteConfig(
    val version: String,
    val buildTime: String = Instant.now().toString(),
    val architectures: Map<String, ArchInfo>
)

/**
 * 版本信息
 */
data class VersionInfo(
    val pluginVersion: String,
    val architecture: String,
    val buildTime: String,
    val checksum: String? = null
)

/**
 * OSS对象信息
 */
data class ObjectInfo(
    val key: String,
    val size: Long,
    val lastModified: Instant,
    val etag: String
)
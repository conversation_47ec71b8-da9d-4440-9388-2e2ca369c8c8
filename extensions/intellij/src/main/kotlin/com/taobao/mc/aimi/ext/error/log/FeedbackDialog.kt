package com.taobao.mc.aimi.ext.error.log

import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBScrollPane
import com.intellij.ui.components.JBTextArea
import com.intellij.util.ui.JBUI
import java.awt.Dimension
import java.awt.GridBagConstraints
import java.awt.GridBagLayout
import javax.swing.*

/**
 * 问题反馈对话框
 */
class FeedbackDialog() : DialogWrapper(true) {

    // 功能选择
    private val functionGroup = ButtonGroup()
    private val completionRadio = JRadioButton("补全", true)
    private val chatRadio = JRadioButton("对话")

    // 分类选择
    private val categoryGroup = ButtonGroup()
    private val experienceRadio = JRadioButton("体验", true)
    private val performanceRadio = JRadioButton("性能")
    private val errorRadio = JRadioButton("报错")
    private val requestRadio = JRadioButton("需求")

    // 说明文本框
    private val descriptionArea = JBTextArea(6, 40)

    init {
        init()
        title = "问题反馈"

        // 设置功能选择按钮组
        functionGroup.add(completionRadio)
        functionGroup.add(chatRadio)

        // 设置分类选择按钮组
        categoryGroup.add(experienceRadio)
        categoryGroup.add(performanceRadio)
        categoryGroup.add(errorRadio)
        categoryGroup.add(requestRadio)

        // 设置文本框属性
        descriptionArea.emptyText.text = "请输入问题描述..."
        descriptionArea.lineWrap = true
        descriptionArea.wrapStyleWord = true
        // 设置内边距
        descriptionArea.border = BorderFactory.createCompoundBorder(
            descriptionArea.border,
            BorderFactory.createEmptyBorder(8, 8, 8, 8)
        )

        // 修改取消/确认的按钮文字
        setOKButtonText("确认")
        setCancelButtonText("取消")
    }

    override fun createCenterPanel(): JComponent {
        val panel = JPanel(GridBagLayout())
        val gbc = GridBagConstraints()

        // 功能标签
        gbc.gridx = 0
        gbc.gridy = 0
        gbc.anchor = GridBagConstraints.WEST
        gbc.insets = JBUI.insets(10, 10, 5, 10)
        panel.add(JBLabel("功能"), gbc)

        // 功能选择面板
        val functionPanel = JPanel()
        functionPanel.layout = BoxLayout(functionPanel, BoxLayout.X_AXIS)
        functionPanel.add(completionRadio)
        functionPanel.add(Box.createHorizontalStrut(20))
        functionPanel.add(chatRadio)

        gbc.gridx = 0
        gbc.gridy = 1
        gbc.fill = GridBagConstraints.HORIZONTAL
        gbc.insets = JBUI.insets(0, 10, 15, 10)
        panel.add(functionPanel, gbc)

        // 分类标签
        gbc.gridx = 0
        gbc.gridy = 2
        gbc.fill = GridBagConstraints.NONE
        gbc.anchor = GridBagConstraints.WEST
        gbc.insets = JBUI.insets(0, 10, 5, 10)
        panel.add(JBLabel("分类"), gbc)

        // 分类选择面板
        val categoryPanel = JPanel()
        categoryPanel.layout = BoxLayout(categoryPanel, BoxLayout.X_AXIS)
        categoryPanel.add(experienceRadio)
        categoryPanel.add(Box.createHorizontalStrut(15))
        categoryPanel.add(performanceRadio)
        categoryPanel.add(Box.createHorizontalStrut(15))
        categoryPanel.add(errorRadio)
        categoryPanel.add(Box.createHorizontalStrut(15))
        categoryPanel.add(requestRadio)

        gbc.gridx = 0
        gbc.gridy = 3
        gbc.fill = GridBagConstraints.HORIZONTAL
        gbc.insets = JBUI.insets(0, 10, 15, 10)
        panel.add(categoryPanel, gbc)

        // 说明标签
        gbc.gridx = 0
        gbc.gridy = 4
        gbc.fill = GridBagConstraints.NONE
        gbc.anchor = GridBagConstraints.WEST
        gbc.insets = JBUI.insets(0, 10, 5, 10)
        panel.add(JBLabel("说明"), gbc)

        // 说明文本框（带滚动条）
        val scrollPane = JBScrollPane(descriptionArea)
        scrollPane.preferredSize = Dimension(500, 120)

        gbc.gridx = 0
        gbc.gridy = 5
        gbc.fill = GridBagConstraints.BOTH
        gbc.weightx = 1.0
        gbc.weighty = 1.0
        gbc.insets = JBUI.insets(0, 10, 10, 10)
        panel.add(scrollPane, gbc)

        return panel
    }

    override fun doOKAction() {
        val description = descriptionArea.text.trim()
        if (description.isEmpty()) {
            setErrorText("请输入问题描述")
            return
        }
        super.doOKAction()
    }

    /**
     * 获取选中的功能类型
     */
    fun getSelectedFunction(): String {
        return when {
            completionRadio.isSelected -> "补全"
            chatRadio.isSelected -> "对话"
            else -> "未知"
        }
    }

    /**
     * 获取选中的分类
     */
    fun getSelectedCategory(): String {
        return when {
            experienceRadio.isSelected -> "体验"
            performanceRadio.isSelected -> "性能"
            errorRadio.isSelected -> "报错"
            requestRadio.isSelected -> "需求"
            else -> "未知"
        }
    }

    /**
     * 获取问题描述
     */
    fun getDescription(): String {
        return descriptionArea.text.trim()
    }

    /**
     * 获取格式化的反馈内容
     */
    fun getExtras(): Map<String, String> {
        return mapOf(
            "ability" to getSelectedFunction(),
            "category" to getSelectedCategory(),
        )
    }
}
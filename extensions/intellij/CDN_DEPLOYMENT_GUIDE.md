# AIMI IntelliJ插件 CDN分发部署指南

## 概述

本指南介绍如何配置和使用AIMI IntelliJ插件的CDN分发机制，该机制可以将二进制文件上传到阿里云OSS，并在插件启动时自动下载和校验。

## 功能特性

1. **自动版本校验** - 插件启动时自动检查本地二进制文件版本
2. **CDN分发** - 支持将二进制文件上传到阿里云OSS CDN
3. **按架构分发** - 支持不同CPU架构的二进制文件分别上传和下载
4. **进度显示** - 下载过程中显示实时进度条
5. **错误处理** - 完善的错误处理和日志记录机制

## 配置说明

### 1. OSS配置

在构建时需要设置以下环境变量或Gradle属性：

```bash
# 启用OSS上传
-Paimi.oss.enabled=true

# OSS访问凭证
-Paimi.oss.accessKeyId=YOUR_ACCESS_KEY_ID
-Paimi.oss.accessKeySecret=YOUR_ACCESS_KEY_SECRET

# OSS配置
-Paimi.oss.endpoint=https://oss-cn-zhangjiakou.aliyuncs.com
-Paimi.oss.bucketName=aimi-ide

# 可选配置
-Paimi.git.commit=COMMIT_HASH
-Paimi.oss.failOnError=false
```

### 2. 构建命令示例

```bash
# 启用OSS上传的构建
./gradlew prepareSandbox \
  -Paimi.oss.enabled=true \
  -Paimi.oss.accessKeyId=YOUR_KEY \
  -Paimi.oss.accessKeySecret=YOUR_SECRET \
  -Paimi.oss.endpoint=https://oss-cn-zhangjiakou.aliyuncs.com \
  -Paimi.oss.bucketName=aimi-ide

# 不启用OSS上传的构建（默认行为）
./gradlew prepareSandbox
```

## 工作流程

### 构建阶段

1. **prepareSandbox任务执行**
   - 复制二进制文件到插件core目录
   - 重命名`aimi-binary`为`aimi-binary-intellij`

2. **版本信息添加**
   - 在每个架构目录中创建`version.json`文件
   - 包含插件版本、构建时间、目标平台等信息

3. **压缩和上传**（如果启用OSS）
   - 按架构分别压缩二进制文件
   - 上传到OSS，路径格式：`aimi-binary/{version}/{arch}/aimi-binary-{arch}.zip`
   - 添加版本、架构、构建时间等元数据

4. **配置文件生成**
   - 生成`src/main/resources/remote.json`配置文件
   - 包含各架构的下载URL和版本信息

5. **占位文件创建**
   - 将原始二进制文件替换为0字节占位文件
   - 减少插件包大小

### 运行时阶段

1. **版本校验**
   - 插件启动时检查本地二进制文件版本
   - 比较插件版本与二进制版本是否匹配

2. **下载和安装**（如果需要）
   - 从`remote.json`读取下载配置
   - 显示后台进度条下载压缩包
   - 解压到core目录并设置权限

3. **启动二进制进程**
   - 验证二进制文件完整性
   - 启动AIMI核心进程

## 文件结构

```
extensions/intellij/
├── src/main/
│   ├── kotlin/com/taobao/mc/aimi/ext/
│   │   ├── oss/                    # OSS上传相关
│   │   │   ├── OSSConfig.kt
│   │   │   ├── OSSUploadService.kt
│   │   │   └── BinaryUploadManager.kt
│   │   ├── config/                 # 配置文件相关
│   │   │   ├── RemoteConfig.kt
│   │   │   └── RemoteConfigGenerator.kt
│   │   ├── version/                # 版本管理
│   │   │   ├── VersionInfo.kt
│   │   │   └── VersionManager.kt
│   │   ├── download/               # 下载管理
│   │   │   └── BinaryDownloadManager.kt
│   │   ├── gradle/                 # Gradle任务
│   │   │   └── BinaryUploadTask.kt
│   │   └── utils/
│   │       └── ErrorHandler.kt    # 错误处理
│   └── resources/
│       └── remote.json             # 下载配置（构建时生成）
└── build.gradle.kts                # 构建配置
```

## 测试验证

### 1. 构建测试

```bash
# 测试不启用OSS的构建
./gradlew clean prepareSandbox

# 测试启用OSS的构建（需要有效的OSS凭证）
./gradlew clean prepareSandbox -Paimi.oss.enabled=true -Paimi.oss.accessKeyId=... -Paimi.oss.accessKeySecret=...
```

### 2. 运行时测试

1. **版本匹配测试**
   - 使用正常构建的插件，验证启动正常
   - 检查日志中的版本校验信息

2. **版本不匹配测试**
   - 手动修改core目录中的version.json文件
   - 重启插件，验证是否触发下载

3. **下载功能测试**
   - 删除core目录中的二进制文件
   - 重启插件，验证下载和解压功能

### 3. 日志检查

关键日志信息：
- `Checking binary files for plugin version: X.X.X`
- `Binary version validation passed/failed`
- `Downloading binary archive...`
- `Binary download completed successfully`

## 故障排除

### 常见问题

1. **OSS上传失败**
   - 检查访问凭证是否正确
   - 检查网络连接和OSS服务状态
   - 查看构建日志中的详细错误信息

2. **下载失败**
   - 检查网络连接
   - 验证remote.json文件内容
   - 检查OSS文件是否存在

3. **版本校验失败**
   - 检查version.json文件格式
   - 验证插件版本号获取是否正确

### 调试模式

可以通过以下方式启用详细日志：
- 在IDE中设置日志级别为DEBUG
- 查看AIMI插件的日志输出

## 安全注意事项

1. **访问凭证安全**
   - 不要在代码中硬编码OSS凭证
   - 使用环境变量或安全的配置管理

2. **下载验证**
   - 建议添加文件校验和验证
   - 使用HTTPS确保传输安全

3. **权限控制**
   - OSS bucket应设置适当的访问权限
   - 限制上传和下载的IP范围（如果需要）

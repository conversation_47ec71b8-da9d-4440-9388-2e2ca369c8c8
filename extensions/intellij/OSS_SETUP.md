# AIMI 二进制文件上传配置

## 环境变量配置

在构建时需要设置以下环境变量用于OSS上传：

```bash
export OSS_ACCESS_KEY_ID="your-access-key-id"
export OSS_ACCESS_KEY_SECRET="your-access-key-secret"
export OSS_ENDPOINT="oss-cn-zhangjiakou.aliyuncs.com"  # 可选，默认值
export OSS_BUCKET_NAME="aimi-binaries"  # 可选，默认值
export OSS_REGION="cn-zhangjiakou"  # 可选，默认值
```

## 构建流程

1. **prepareSandbox**: 复制二进制文件到插件目录
2. **processBinaries**: 自动执行以下步骤：
   - 为二进制文件添加版本信息
   - 按架构压缩文件
   - 上传到OSS
   - 生成remote.json配置文件
   - 创建占位符文件

## 运行时下载

插件启动时会自动：
1. 检查二进制版本是否匹配
2. 如果版本不匹配或文件不存在，从OSS下载最新版本
3. 后台显示下载进度
4. 解压并替换二进制文件

## 文件结构

```
build/idea-sandbox/plugins/aimi/core/
├── darwin-arm64/
│   ├── aimi-binary-intellij (0字节占位符)
│   ├── index.node
│   ├── rg
│   └── package.json
├── darwin-x64/
│   └── ...
└── linux-x64/
    └── ...
```

```
src/main/resources/
└── remote.json (构建时生成的下载配置)
```
#!/bin/bash

# AIMI IntelliJ插件 CDN分发测试脚本

set -e

echo "=== AIMI CDN Deployment Test Script ==="
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    if ! command -v ./gradlew &> /dev/null; then
        log_error "gradlew not found in current directory"
        exit 1
    fi
    
    if ! command -v java &> /dev/null; then
        log_error "Java not found"
        exit 1
    fi
    
    log_info "Prerequisites check passed"
}

# 清理构建目录
clean_build() {
    log_info "Cleaning build directory..."
    ./gradlew clean
    log_info "Build directory cleaned"
}

# 测试不启用OSS的构建
test_normal_build() {
    log_info "Testing normal build (OSS disabled)..."
    
    ./gradlew prepareSandbox
    
    # 检查core目录是否存在
    if [ -d "build/idea-sandbox/plugins/AIMI/core" ]; then
        log_info "Normal build successful - core directory created"
        
        # 检查二进制文件
        if find build/idea-sandbox/plugins/AIMI/core -name "aimi-binary-intellij*" | grep -q .; then
            log_info "Binary files found in core directory"
        else
            log_warn "No binary files found in core directory"
        fi
    else
        log_error "Normal build failed - core directory not found"
        return 1
    fi
}

# 测试启用OSS的构建（需要凭证）
test_oss_build() {
    log_info "Testing OSS build..."
    
    # 检查OSS凭证
    if [ -z "$AIMI_OSS_ACCESS_KEY_ID" ] || [ -z "$AIMI_OSS_ACCESS_KEY_SECRET" ]; then
        log_warn "OSS credentials not provided, skipping OSS build test"
        log_warn "Set AIMI_OSS_ACCESS_KEY_ID and AIMI_OSS_ACCESS_KEY_SECRET to test OSS upload"
        return 0
    fi
    
    log_info "OSS credentials found, testing upload..."
    
    ./gradlew prepareSandbox \
        -Paimi.oss.enabled=true \
        -Paimi.oss.accessKeyId="$AIMI_OSS_ACCESS_KEY_ID" \
        -Paimi.oss.accessKeySecret="$AIMI_OSS_ACCESS_KEY_SECRET" \
        -Paimi.oss.endpoint=https://oss-cn-zhangjiakou.aliyuncs.com \
        -Paimi.oss.bucketName=aimi-ide-test \
        -Paimi.git.commit="test-commit"
    
    # 检查remote.json是否生成
    if [ -f "src/main/resources/remote.json" ]; then
        log_info "remote.json generated successfully"
        log_info "Content preview:"
        head -10 src/main/resources/remote.json
    else
        log_error "remote.json not generated"
        return 1
    fi
    
    # 检查占位文件
    if find build/idea-sandbox/plugins/AIMI/core -name "aimi-binary-intellij*" -size 0 | grep -q .; then
        log_info "Placeholder files created successfully"
    else
        log_warn "Placeholder files not found or not empty"
    fi
}

# 验证版本信息文件
test_version_files() {
    log_info "Testing version file generation..."
    
    # 查找version.json文件
    version_files=$(find build/idea-sandbox/plugins/AIMI/core -name "version.json" 2>/dev/null || true)
    
    if [ -n "$version_files" ]; then
        log_info "Version files found:"
        echo "$version_files"
        
        # 显示第一个版本文件的内容
        first_file=$(echo "$version_files" | head -1)
        log_info "Sample version file content:"
        cat "$first_file"
    else
        log_warn "No version.json files found"
    fi
}

# 测试配置文件解析
test_config_parsing() {
    log_info "Testing configuration parsing..."
    
    if [ -f "src/main/resources/remote.json" ]; then
        # 简单的JSON格式验证
        if python3 -m json.tool src/main/resources/remote.json > /dev/null 2>&1; then
            log_info "remote.json is valid JSON"
        elif python -m json.tool src/main/resources/remote.json > /dev/null 2>&1; then
            log_info "remote.json is valid JSON"
        else
            log_error "remote.json is not valid JSON"
            return 1
        fi
    else
        log_warn "remote.json not found, skipping parsing test"
    fi
}

# 显示构建统计信息
show_build_stats() {
    log_info "Build statistics:"
    
    if [ -d "build/idea-sandbox/plugins/AIMI" ]; then
        plugin_size=$(du -sh build/idea-sandbox/plugins/AIMI | cut -f1)
        log_info "Plugin size: $plugin_size"
        
        core_size=$(du -sh build/idea-sandbox/plugins/AIMI/core 2>/dev/null | cut -f1 || echo "N/A")
        log_info "Core directory size: $core_size"
        
        # 统计文件数量
        total_files=$(find build/idea-sandbox/plugins/AIMI -type f | wc -l)
        core_files=$(find build/idea-sandbox/plugins/AIMI/core -type f 2>/dev/null | wc -l || echo "0")
        log_info "Total files: $total_files, Core files: $core_files"
    fi
}

# 主测试流程
main() {
    echo "Starting CDN deployment tests..."
    echo "Current directory: $(pwd)"
    echo "Date: $(date)"
    echo

    check_prerequisites
    echo

    clean_build
    echo

    test_normal_build
    echo

    test_version_files
    echo

    test_oss_build
    echo

    test_config_parsing
    echo

    show_build_stats
    echo

    log_info "All tests completed!"
    echo
    echo "=== Test Summary ==="
    echo "✓ Normal build test"
    echo "✓ Version file generation test"
    if [ -n "$AIMI_OSS_ACCESS_KEY_ID" ]; then
        echo "✓ OSS upload test"
        echo "✓ Configuration parsing test"
    else
        echo "⚠ OSS upload test (skipped - no credentials)"
        echo "⚠ Configuration parsing test (skipped - no remote.json)"
    fi
    echo "✓ Build statistics"
}

# 运行主函数
main "$@"

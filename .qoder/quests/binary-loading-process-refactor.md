# AIMI二进制加载流程重构设计方案（优化版）

## 概述

采用buildSrc统一任务实现构建时上传，通过OSS代码一致性迁移确保上传和下载功能的完全兼容。

## 核心优化

### 1. buildSrc统一任务

```kotlin
// buildSrc/src/main/kotlin/BinaryProcessTask.kt
abstract class BinaryProcessTask : DefaultTask() {
    @TaskAction
    fun processAll() {
        val architectures = listOf("darwin-arm64", "darwin-x64", "linux-x64")
        val architectureInfos = mutableMapOf<String, ArchInfo>()

        architectures.forEach { arch ->
            // 1. 压缩架构目录
            val zipFile = compressArchitecture(arch)
            // 2. 上传到OSS
            val cdnUrl = uploadToOSS(zipFile, arch)
            // 3. 记录信息
            architectureInfos[arch] = ArchInfo(cdnUrl, zipFile.length())
        }

        // 4. 生成remote.json
        generateRemoteConfig(architectureInfos)
        // 5. 创建占位符文件
        createPlaceholderFiles(architectures)
    }
}
```

### 2. OSS统一接口（确保一致性）

```kotlin
// 共享接口
interface OSSService {
    fun upload(objectKey: String, file: File, metadata: Map<String, String>): String
    fun download(objectKey: String, targetFile: File, progressCallback: (Long, Long) -> Unit): Boolean
    fun getObjectInfo(objectKey: String): ObjectInfo?
}

// 构建时实现
class BuildTimeOSSService(config: OSSConfig) : OSSService {
    private val ossClient = OSSClientBuilder().build(/*...*/)

    override fun upload(objectKey: String, file: File, metadata: Map<String, String>): String {
        val request = PutObjectRequest(bucketName, objectKey, file.inputStream())
        request.metadata = ObjectMetadata().apply {
            metadata.forEach { (k, v) -> setHeader("x-oss-meta-$k", v) }
        }
        ossClient.putObject(request)
        return "https://$bucketName.$endpoint/$objectKey"
    }
}

// 运行时实现
class RuntimeOSSService(config: OSSConfig) : OSSService {
    private val httpClient = OkHttpClient()

    override fun download(objectKey: String, targetFile: File, progressCallback: (Long, Long) -> Unit): Boolean {
        val url = "https://${config.bucketName}.${config.endpoint}/$objectKey"
        val response = httpClient.newCall(Request.Builder().url(url).build()).execute()

        if (!response.isSuccessful) return false

        response.body?.byteStream()?.use { input ->
            targetFile.outputStream().use { output ->
                input.copyTo(output) { bytes -> progressCallback(bytes, response.body!!.contentLength()) }
            }
        }
        return true
    }
}
```

### 3. 插件运行时下载

```kotlin
class BinaryDownloadManager {
    private val ossService = RuntimeOSSService(OSSConfig.fromEnvironment())

    fun validateAndDownload(): Boolean {
        if (!needsUpdate()) return true

        val remoteConfig = loadRemoteConfig()
        val archInfo = remoteConfig.architectures[getCurrentArch()] ?: return false

        // 从CDN URL提取OSS objectKey
        val objectKey = extractObjectKey(archInfo.url)
        val tempFile = createTempFile()

        return ossService.download(objectKey, tempFile) { downloaded, total ->
            updateProgress(downloaded, total)
        }
    }

    private fun extractObjectKey(cdnUrl: String): String {
        // 从"https://bucket.endpoint/path/to/file.zip"提取"path/to/file.zip"
        return URI(cdnUrl).path.removePrefix("/")
    }
}
```

## 关键优化点

### 1. 任务统一化

- 将三个分散任务合并为一个原子操作
- 确保所有架构处理的一致性
- 统一的错误处理和日志

### 2. OSS一致性保证

- 相同的objectKey生成规则：`aimi-plugin/core/{version}/{arch}/core.zip`
- 相同的元数据格式：`x-oss-meta-*`头部
- 统一的错误处理机制

### 3. 运行时兼容

- 从已知的CDN URL提取OSS objectKey
- 使用相同的下载路径和校验机制
- 支持后台下载和进度显示

## 实施计划

1. **OSS接口统一**：定义通用接口，两端实现
2. **合并buildSrc任务**：三个任务合并为一个
3. **迁移OSS代码**：确保构建和运行时的完全兼容
4. **集成测试**：验证上传和下载的一致性

## 架构设计

```mermaid
flowchart TD
    A[buildSrc插件执行] --> B[压缩架构文件]
    B --> C[OSS上传-buildSrc版本]
    C --> D[生成remote.json]
    D --> E[创建占位符文件]

    F[插件启动] --> G[版本校验]
    G --> H{版本匹配?}
    H -->|否| I[OSS下载-插件版本]
    I --> J[后台进度显示]
    J --> K[解压替换]
    H -->|是| L[正常启动]
    K --> L
```

## 核心实现

### 1. buildSrc插件架构

#### 目录结构

```
buildSrc/
├── build.gradle.kts
├── src/main/kotlin/
│   └── com.aimi.gradle/
│       ├── BinaryUploadPlugin.kt
│       ├── tasks/
│       │   ├── CompressArchitectureTask.kt
│       │   ├── UploadToOSSTask.kt
│       │   └── GenerateRemoteConfigTask.kt
│       └── oss/
│           ├── BuildTimeOSSUploader.kt  # 构建时OSS上传
│           ├── OSSConfig.kt
│           └── VersionInfo.kt
```

#### 主插件类

```kotlin
class BinaryUploadPlugin : Plugin<Project> {
    override fun apply(project: Project) {
        val extension = project.extensions.create("binaryUpload", BinaryUploadExtension::class.java)

        // 注册任务
        val compressTask = project.tasks.register("compressArchitectures", CompressArchitectureTask::class.java)
        val uploadTask = project.tasks.register("uploadBinaries", UploadToOSSTask::class.java)
        val configTask = project.tasks.register("generateRemoteConfig", GenerateRemoteConfigTask::class.java)

        // 任务依赖关系
        uploadTask.configure { dependsOn(compressTask) }
        configTask.configure { dependsOn(uploadTask) }

        // 集成到prepareSandbox
        project.tasks.named("prepareSandbox") {
            finalizedBy(configTask)
        }
    }
}
```

#### buildSrc的build.gradle.kts

```kotlin
plugins {
    `kotlin-dsl`
}

repositories {
    gradlePluginPortal()
    mavenCentral()
}

dependencies {
    implementation("com.aliyun.openservices:aliyun-openservices:1.2.3")
    implementation("com.google.code.gson:gson:2.8.9")
}
```

#### 压缩任务实现

```kotlin
abstract class CompressArchitectureTask : DefaultTask() {

    @get:InputDirectory
    abstract val coreDirectory: DirectoryProperty

    @get:OutputDirectory
    abstract val outputDirectory: DirectoryProperty

    @TaskAction
    fun compress() {
        val architectures = listOf("darwin-arm64", "darwin-x64", "linux-x64")
        val version = project.version.toString()

        architectures.forEach { arch ->
            processArchitecture(arch, version)
        }
    }

    private fun processArchitecture(arch: String, version: String) {
        val archDir = coreDirectory.dir(arch).get().asFile
        if (!archDir.exists()) return

        // 1. 添加版本信息到二进制文件
        val binaryFile = File(archDir, "aimi-binary-intellij")
        VersionInfoAppender.appendVersionInfo(binaryFile, version, arch)

        // 2. 压缩整个目录
        val zipFile = outputDirectory.file("aimi-core-${version}-${arch}.zip").get().asFile
        ZipUtils.zipDirectory(archDir, zipFile)

        logger.info("Compressed ${arch} architecture to ${zipFile.path}")
    }
}
```

### 2. OSS代码分布策略

#### 构建时OSS上传器（buildSrc版本）

```kotlin
// buildSrc/src/main/kotlin/com.aimi.gradle/oss/BuildTimeOSSUploader.kt
class BuildTimeOSSUploader(private val config: OSSConfig) {
    private val ossClient: OSS by lazy {
        OSSClientBuilder().build(
            config.endpoint,
            config.accessKeyId,
            config.accessKeySecret
        )
    }

    fun uploadBinary(file: File, arch: String, version: String): String {
        val objectKey = "aimi-plugin/core/${version}/${arch}/core.zip"

        val metadata = ObjectMetadata().apply {
            setHeader("x-oss-meta-version", version)
            setHeader("x-oss-meta-architecture", arch)
            setHeader("x-oss-meta-build-time", System.currentTimeMillis().toString())
            contentLength = file.length()
        }

        val request = PutObjectRequest(config.bucketName, objectKey, file.inputStream())
        request.metadata = metadata

        ossClient.putObject(request)

        return "https://${config.bucketName}.${config.endpoint}/${objectKey}"
    }

    fun close() {
        ossClient.shutdown()
    }
}
```

#### 运行时OSS下载器（插件版本）

```kotlin
// extensions/intellij/src/main/kotlin/com/taobao/mc/aimi/oss/RuntimeOSSDownloader.kt
class RuntimeOSSDownloader {
    private val logger = LoggerManager.getLogger(javaClass)

    fun downloadBinary(
        url: String,
        targetFile: File,
        progressCallback: (Long, Long) -> Unit = { _, _ -> }
    ): Boolean {
        return try {
            val client = OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .build()

            val request = Request.Builder().url(url).build()
            val response = client.newCall(request).execute()

            if (!response.isSuccessful) {
                logger.error("Download failed: ${response.code}")
                return false
            }

            val body = response.body ?: return false
            val contentLength = body.contentLength()

            targetFile.parentFile.mkdirs()
            FileOutputStream(targetFile).use { output ->
                body.byteStream().use { input ->
                    val buffer = ByteArray(8192)
                    var bytesRead = 0
                    var totalBytesRead = 0L

                    while (input.read(buffer).also { bytesRead = it } != -1) {
                        output.write(buffer, 0, bytesRead)
                        totalBytesRead += bytesRead
                        progressCallback(totalBytesRead, contentLength)
                    }
                }
            }
            true
        } catch (e: Exception) {
            logger.error("Download error", e)
            false
        }
    }
}
```

### 3. 共享OSS配置

#### 配置类（两端共享）

```kotlin
data class OSSConfig(
    val endpoint: String = "oss-cn-zhangjiakou.aliyuncs.com",
    val bucketName: String = "aimi-binaries",
    val accessKeyId: String = System.getenv("OSS_ACCESS_KEY_ID") ?: "",
    val accessKeySecret: String = System.getenv("OSS_ACCESS_KEY_SECRET") ?: "",
    val region: String = "cn-zhangjiakou"
) {
    fun validate(): Boolean {
        return endpoint.isNotEmpty() &&
               bucketName.isNotEmpty() &&
               accessKeyId.isNotEmpty() &&
               accessKeySecret.isNotEmpty()
    }
}
```

### 4. 插件中的版本校验与下载

#### 扩展AIMIBinaryProcess

```kotlin
class AIMIBinaryProcess(private val onUnexpectedExit: () -> Unit) : AIMIProcess {
    private val logger = LoggerManager.getLogger(javaClass)
    private val downloader = RuntimeOSSDownloader()

    private fun startBinaryProcess(): Process {
        // 启动前进行版本校验
        if (!validateAndUpdateBinary()) {
            throw RuntimeException("Failed to validate or update binary")
        }

        val path = getAIMIBinaryPath()
        // ... 原有启动逻辑
    }

    private fun validateAndUpdateBinary(): Boolean {
        val binaryPath = getAIMIBinaryPath()
        val binaryFile = File(binaryPath)

        // 检查是否为占位符文件
        if (!binaryFile.exists() || binaryFile.length() == 0L) {
            logger.info("Placeholder file detected, downloading binary...")
            return downloadBinaryFromRemote()
        }

        // 检查版本是否匹配
        val localVersion = BinaryVersionReader().readVersionInfo(binaryFile)
        val pluginVersion = getCurrentPluginVersion()

        if (localVersion?.pluginVersion != pluginVersion) {
            logger.info("Version mismatch: local=${localVersion?.pluginVersion}, plugin=$pluginVersion")
            return downloadBinaryFromRemote()
        }

        return true
    }

    private fun downloadBinaryFromRemote(): Boolean {
        // 在后台任务中执行下载
        val project = ProjectManager.getInstance().openProjects.firstOrNull() ?: return false

        val task = object : Task.Backgroundable(project, "下载AIMI核心组件", true) {
            override fun run(indicator: ProgressIndicator) {
                performDownload(indicator)
            }
        }

        var downloadSuccess = false
        val latch = CountDownLatch(1)

        task.queue()
        // 等待下载完成（带超时）
        return latch.await(120, TimeUnit.SECONDS) && downloadSuccess
    }
}
```

### 5. remote.json配置管理

#### 生成任务

```kotlin
abstract class GenerateRemoteConfigTask : DefaultTask() {

    @get:Input
    abstract val architectureInfos: MapProperty<String, ArchInfo>

    @get:OutputFile
    abstract val outputFile: RegularFileProperty

    @TaskAction
    fun generate() {
        val config = RemoteConfig(
            version = project.version.toString(),
            buildTime = Instant.now().toString(),
            architectures = architectureInfos.get()
        )

        val gson = GsonBuilder().setPrettyPrinting().create()
        outputFile.get().asFile.writeText(gson.toJson(config))

        logger.info("Generated remote config: ${outputFile.get().asFile.path}")
    }
}
```

#### 配置读取

```kotlin
class RemoteConfigReader {
    fun loadConfig(): RemoteConfig? {
        return try {
            val resourceStream = javaClass.getResourceAsStream("/remote.json")
            val configJson = resourceStream?.bufferedReader()?.use { it.readText() }
            configJson?.let { Gson().fromJson(it, RemoteConfig::class.java) }
        } catch (e: Exception) {
            null
        }
    }
}
```

## 关键技术点

### 1. OSS代码分离策略

- **buildSrc版本**：专注于构建时上传，依赖完整的OSS SDK
- **插件版本**：专注于运行时下载，使用轻量级HTTP客户端
- **共享配置**：两端共享配置类和数据模型

### 2. 构建插件集成

- 使用Gradle插件系统确保构建流程的可维护性
- 任务依赖关系清晰，支持增量构建
- 环境变量配置，支持CI/CD集成

### 3. 错误处理机制

- 网络超时和重试策略
- 占位符文件作为fallback机制
- 详细的日志记录和错误报告

### 4. 性能优化

- 后台异步下载，不阻塞插件启动
- 进度回调机制，用户体验友好
- 版本缓存避免重复下载

## 实施步骤

1. **创建buildSrc插件**：实现构建时上传逻辑
2. **集成OSS上传**：添加构建时OSS客户端
3. **实现插件下载**：添加运行时OSS下载器
4. **版本校验机制**：扩展现有的版本检查逻辑
5. **测试与优化**：端到端测试和性能调优
